import 'package:flutter/material.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';
import 'package:e_sasthra/services/tutorial_service.dart';

/// Manager for the tutorial coach mark feature
class TutorialManager {
  /// Create the tutorial coach mark
  static TutorialCoachMark createTutorial({
    required BuildContext context,
    required GlobalKey ropeKey,
    required GlobalKey editProfileKey,
    List<GlobalKey>? additionalKeys,
    List<String>? additionalDescriptions,
  }) {
    // Store screen size
    final screenSize = MediaQuery.of(context).size;

    // Create targets for the tutorial
    final targets = [
      // Rope animation target
      TargetFocus(
        identify: 'rope_target',
        keyTarget: ropeKey,
        alignSkip: Alignment.bottomRight,
        shape: ShapeLightFocus.RRect,
        radius: 15,
        paddingFocus: 8,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            builder: (context, controller) {
              return _buildContentWidget(
                title: 'Pro Subscription',
                description:
                    'Pull this rope to navigate to the Pro Subscription page with premium features.',
                icon: Icons.arrow_upward,
              );
            },
          ),
        ],
      ),

      // Edit profile target
      TargetFocus(
        identify: 'edit_profile_target',
        keyTarget: editProfileKey,
        alignSkip: Alignment.bottomRight,
        shape: ShapeLightFocus.Circle,
        paddingFocus: 5,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            builder: (context, controller) {
              return _buildContentWidget(
                title: 'Edit Profile',
                description:
                    'By clicking this, you can view your profile details and update them.',
                icon: Icons.edit,
              );
            },
          ),
        ],
      ),

      // Explorer icon target - using fixed position
      TargetFocus(
        identify: 'explore_target',
        targetPosition: TargetPosition(
          Size(50, 60),
          Offset(screenSize.width * 0.25 + 25, screenSize.height - 65),
        ),
        alignSkip: Alignment.topRight,
        shape: ShapeLightFocus.Circle,
        paddingFocus: 5,
        contents: [
          TargetContent(
            align: ContentAlign.top,
            builder: (context, controller) {
              return _buildContentWidget(
                title: 'Explore',
                description:
                    'Tap here to access various assessment options and learning modules.',
                icon: Icons.explore,
              );
            },
          ),
        ],
      ),

      // Scenario module target - using fixed position
      TargetFocus(
        identify: 'scenario_target',
        targetPosition: TargetPosition(
          Size(50, 60),
          Offset(screenSize.width * 0.5 + 15, screenSize.height - 65),
        ),
        alignSkip: Alignment.topRight,
        shape: ShapeLightFocus.Circle,
        paddingFocus: 5,
        contents: [
          TargetContent(
            align: ContentAlign.top,
            builder: (context, controller) {
              return _buildContentWidget(
                title: 'Restaurant Reservation',
                description:
                    'Practice making  reservations and dining conversations in English.',
                icon: Icons.restaurant,
              );
            },
          ),
        ],
      ),

      // Role module target - using fixed position
      TargetFocus(
        identify: 'role_module_target',
        targetPosition: TargetPosition(
          Size(50, 60),
          Offset(screenSize.width * 0.75 + 25, screenSize.height - 65),
        ),
        alignSkip: Alignment.topRight,
        shape: ShapeLightFocus.Circle,
        paddingFocus: 5,
        contents: [
          TargetContent(
            align: ContentAlign.top,
            builder: (context, controller) {
              return _buildContentWidget(
                title: 'Dining Experience',
                description:
                    'Practice  conversations and improve your dining vocabulary.',
                icon: Icons.dinner_dining,
              );
            },
          ),
        ],
      ),
    ];

    // Add additional targets (e.g., bell icon)
    if (additionalKeys != null &&
        additionalDescriptions != null &&
        additionalKeys.length == additionalDescriptions.length) {
      for (int i = 0; i < additionalKeys.length; i++) {
        targets.add(
          TargetFocus(
            identify: 'additional_target_$i',
            keyTarget: additionalKeys[i],
            alignSkip: Alignment.bottomRight,
            shape: ShapeLightFocus.Circle,
            paddingFocus: 5,
            contents: [
              TargetContent(
                align: ContentAlign.bottom,
                builder: (context, controller) {
                  return _buildContentWidget(
                    title: 'Notifications',
                    description: additionalDescriptions[i],
                    icon: Icons.notifications,
                  );
                },
              ),
            ],
          ),
        );
      }
    }

    // Create and return tutorial coach mark
    return TutorialCoachMark(
      targets: targets,
      colorShadow: const Color(0xFF106466),
      textSkip: "SKIP",
      textStyleSkip: const TextStyle(
        color: Colors.white,
        fontWeight: FontWeight.bold,
        fontSize: 26,
      ),
      paddingFocus: 30,
      opacityShadow: 0.85,
      hideSkip: false,
      alignSkip: Alignment.bottomRight,
      onFinish: () {
        TutorialService.markTutorialAsShown();
      },
      onSkip: () {
        TutorialService.markTutorialAsShown();
        return true;
      },
    );
  }

  /// Check if the tutorial has been shown before
  static Future<bool> isTutorialShown() async {
    return await TutorialService.isTutorialShown();
  }

  /// Mark the tutorial as shown
  static Future<void> markTutorialAsShown() async {
    await TutorialService.markTutorialAsShown();
  }

  /// Show the tutorial coach mark
  static void showTutorial({
    required BuildContext context,
    required GlobalKey ropeKey,
    required GlobalKey editProfileKey,
    List<GlobalKey>? additionalKeys,
    List<String>? additionalDescriptions,
  }) {
    final tutorialCoachMark = createTutorial(
      context: context,
      ropeKey: ropeKey,
      editProfileKey: editProfileKey,
      additionalKeys: additionalKeys,
      additionalDescriptions: additionalDescriptions,
    );

    tutorialCoachMark.show(context: context);
  }

  /// Build a content widget for the tutorial
  static Widget _buildContentWidget({
    required String title,
    required String description,
    required IconData icon,
  }) {
    return Container(
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: const Color(0xFF106466), size: 24),
              const SizedBox(width: 10),
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF106466),
                  fontSize: 20.0,
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
          Text(
            description,
            style: const TextStyle(
              color: Colors.black87,
              fontSize: 16.0,
            ),
          ),
        ],
      ),
    );
  }
}
