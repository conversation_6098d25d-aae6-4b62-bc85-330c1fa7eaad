import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:audioplayers/audioplayers.dart';
import 'package:record/record.dart' as record;
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:e_sasthra/user_provider.dart' as user_provider;

// Color palette for nebula-themed dark blue/purple theme
const Color cosmicBackground = Color(0xFF0A0A1F);
const Color nebulaPurple = Color(0xFF2A1B4D);
const Color nebulaBlue = Color(0xFF1A2A6C);
const Color nebulaPink = Color(0xFFB14AFF);
const Color nebulaTeal = Color(0xFF00D4FF);
const Color starWhite = Color(0xFFFFFFFF);
const Color starGold = Color(0xFFFFD700);

class RolePlayChatScreen extends StatefulWidget {
  final String sessionId;
  final String themeTopic;
  final String userRole;
  final String botRole;
  final Map<String, dynamic> initialMessage;

  const RolePlayChatScreen({
    super.key,
    required this.sessionId,
    required this.themeTopic,
    required this.userRole,
    required this.botRole,
    required this.initialMessage,
  });

  @override
  _RolePlayChatScreenState createState() => _RolePlayChatScreenState();
}

class _RolePlayChatScreenState extends State<RolePlayChatScreen> {
  final List<Map<String, dynamic>> messages = [];
  final AudioPlayer audioPlayer = AudioPlayer();
  final record.AudioRecorder _audioRecorder = record.AudioRecorder();
  String? recordingPath;
  bool _isRecording = false;
  bool _isPlaying = false;
  String? _currentlyPlayingId;
  String? _statusMessage;
  final Map<String, bool> _translationVisibility = {};

  @override
  void initState() {
    super.initState();
    if (widget.initialMessage.isNotEmpty) {
      final fixedInitialMessage = _fixEncoding(widget.initialMessage);
      if (fixedInitialMessage.containsKey('hint')) {
        fixedInitialMessage['hintVisible'] = false;
      }
      messages.add(fixedInitialMessage);
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _playInitialAudioSequentially();
      });
    }
  }

  Future<void> _playInitialAudioSequentially() async {
    if (!mounted || messages.isEmpty) return;
    final initialMessage = messages.first;

    if (initialMessage.containsKey('introduction_audio_base64')) {
      await _playAudio(initialMessage['introduction_audio_base64'], 'intro_0');
    }

    if (!mounted) return;

    if (initialMessage.containsKey('question_audio_base64')) {
      await _playAudio(initialMessage['question_audio_base64'], 'question_0');
    }
  }

  Future<void> _playAudio(String base64Audio, String uniqueId) async {
    if (!mounted || base64Audio.isEmpty) return;

    if (_isPlaying) {
      await audioPlayer.stop();
      if (mounted) setState(() => _isPlaying = false);
    }

    if (mounted) {
      setState(() {
        _isPlaying = true;
        _currentlyPlayingId = uniqueId;
      });
    }

    try {
      final audioBytes = base64Decode(base64Audio);
      await audioPlayer.play(BytesSource(audioBytes));
      await audioPlayer.onPlayerComplete.first;
      if (mounted && _currentlyPlayingId == uniqueId) {
        setState(() {
          _isPlaying = false;
          _currentlyPlayingId = null;
        });
      }
    } catch (e) {
      print('Error playing audio: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error playing audio: $e')),
        );
        if (_currentlyPlayingId == uniqueId) {
          setState(() {
            _isPlaying = false;
            _currentlyPlayingId = null;
          });
        }
      }
    }
  }

  Future<void> _startRecording() async {
    try {
      if (!mounted || _isPlaying) return;

      final hasPermission = await _audioRecorder.hasPermission();
      if (!hasPermission) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Microphone permission not granted')),
          );
        }
        return;
      }

      final tempDir = await getTemporaryDirectory();
      recordingPath =
          '${tempDir.path}/recording_${DateTime.now().millisecondsSinceEpoch}.m4a';

      await _audioRecorder.start(
        const record.RecordConfig(
          encoder: record.AudioEncoder.aacLc,
          bitRate: 128000,
          sampleRate: 44100,
        ),
        path: recordingPath!,
      );

      if (mounted) {
        setState(() {
          _isRecording = true;
          _statusMessage = "Recording...";
        });
      }
    } catch (e) {
      print('Error starting recording: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error starting recording: $e')),
        );
      }
    }
  }

  Future<void> _stopRecording() async {
    try {
      if (!mounted || !_isRecording) return;

      final path = await _audioRecorder.stop();

      if (mounted) {
        setState(() {
          _isRecording = false;
          _statusMessage = "Processing your response...";
        });
      }

      if (path != null) {
        recordingPath = path;
        await _sendAudioToBackend();
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('No audio was recorded')),
          );
        }
      }
    } catch (e) {
      print('Error stopping recording: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error stopping recording: $e')),
        );
      }
    }
  }

  Future<void> _sendAudioToBackend() async {
    if (recordingPath == null || !mounted) return;

    try {
      final audioFile = File(recordingPath!);
      if (!await audioFile.exists()) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Audio file not found')),
          );
        }
        return;
      }

      final audioBytes = await audioFile.readAsBytes();
      final base64Audio = base64Encode(audioBytes);

      final userProvider =
          Provider.of<user_provider.UserProvider>(context, listen: false);
      final userId = userProvider.userId;

      if (userId == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('User ID not found')),
          );
        }
        return;
      }

      setState(() => _statusMessage = "Sending your response...");

      final response = await http
          .post(
            Uri.parse('https://talktoai.in/rolesubmit'),
            headers: {'Content-Type': 'application/json; charset=utf-8'},
            body: jsonEncode({
              'user_id': userId,
              'session_id': widget.sessionId,
              'audio_base64': base64Audio,
            }),
          )
          .timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final rawBytes = response.bodyBytes;
        String decodedBody;

        try {
          decodedBody = utf8.decode(rawBytes, allowMalformed: true);
        } catch (e) {
          print("UTF-8 decoding failed: $e");
          decodedBody = latin1.decode(rawBytes);
          decodedBody = utf8.decode(utf8.encode(decodedBody));
        }

        final data = jsonDecode(decodedBody);
        final fixedMessage = _fixEncoding(data['message']);

        fixedMessage['user_reply_audio_base64'] = base64Audio;

        if (fixedMessage.containsKey('hint')) {
          fixedMessage['hintVisible'] = false;
        }

        if (mounted) {
          setState(() {
            messages.add(fixedMessage);
            _statusMessage = "Response received";
          });

          if (fixedMessage.containsKey('question_audio_base64')) {
            await _playAudio(fixedMessage['question_audio_base64'],
                'question_${messages.length - 1}');
          }

          await Future.delayed(Duration(seconds: 2));
          if (mounted) {
            setState(() => _statusMessage = null);
          }
        }
      } else {
        print('API Error: ${response.statusCode} - ${response.body}');
        if (mounted) {
          setState(() => _statusMessage = "Error: ${response.statusCode}");
        }
      }
    } catch (e) {
      print('Error sending audio: $e');
      if (mounted) {
        setState(() => _statusMessage = "Error: $e");
      }
    } finally {
      if (recordingPath != null) {
        try {
          final file = File(recordingPath!);
          if (await file.exists()) {
            await file.delete();
          }
        } catch (e) {
          print('Error deleting recording file: $e');
        }
      }
    }
  }

  Map<String, dynamic> _fixEncoding(Map<String, dynamic> message) {
    final fixedMessage = Map<String, dynamic>.from(message);
    final indicLanguageRanges = [
      r'[\u0900-\u097F]', // Hindi (Devanagari)
      r'[\u0B80-\u0BFF]', // Tamil
      r'[\u0C00-\u0C7F]', // Telugu
      r'[\u0C80-\u0CFF]', // Kannada
      r'[\u0D00-\u0D7F]', // Malayalam
      r'[\u0980-\u09FF]', // Bengali
    ];
    final indicRegex = RegExp(indicLanguageRanges.join('|'), unicode: true);

    message.forEach((key, value) {
      if (value is String) {
        try {
          String decoded = value;
          if (!indicRegex.hasMatch(value)) {
            final bytes = latin1.encode(value);
            decoded = utf8.decode(bytes, allowMalformed: true);
          }

          if (indicRegex.hasMatch(decoded)) {
            fixedMessage[key] = decoded;
          } else {
            fixedMessage[key] = value;
          }
        } catch (e) {
          print("Encoding fix failed for key $key: $e");
          fixedMessage[key] = value;
        }
      }
    });
    return fixedMessage;
  }

  Widget _buildMessageCard(Map<String, dynamic> message, int messageIndex) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            nebulaPurple.withOpacity(0.7),
            nebulaBlue.withOpacity(0.5),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: nebulaPink.withOpacity(0.2),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ColorFilter.mode(
            Colors.black.withOpacity(0.3),
            BlendMode.darken,
          ),
          child: Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (messageIndex == 0) ...[
                  if (message.containsKey('introduction'))
                    _buildBubbleSection(
                      title: 'Introduction',
                      text: message['introduction'],
                      audioBase64: message['introduction_audio_base64'],
                      isNativeLanguage: false,
                    ),
                  if (message.containsKey('question'))
                    _buildBubbleSection(
                      title: 'Question',
                      text: message['question'],
                      audioBase64: message['question_audio_base64'],
                      translationAudioBase64:
                          message['question_translation_audio_base64'],
                      isNativeLanguage: false,
                      translation: message['question_translation'],
                    ),
                  if (message.containsKey('hint'))
                    if (message['hintVisible'] == true)
                      _buildBubbleSection(
                        title: 'Hint',
                        text: message['hint'],
                        audioBase64: null,
                        isNativeLanguage: false,
                      )
                    else
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              message['hintVisible'] = true;
                            });
                          },
                          child: Icon(
                            Icons.lightbulb_outline,
                            color: nebulaTeal,
                            size: 30,
                          ),
                        ),
                      ),
                ],
                if (message.containsKey('user_reply_audio_base64')) ...[
                  _buildBubbleSection(
                    title: 'Your Response',
                    text: 'Audio response',
                    audioBase64: message['user_reply_audio_base64'],
                    isNativeLanguage: false,
                  ),
                  if (message.containsKey('correction') &&
                      message['correction'] is Map)
                    _buildCorrectionSection(
                      correctionData: message['correction'],
                    ),
                  if (message.containsKey('explanation'))
                    _buildBubbleSection(
                      title: 'Explanation',
                      text: message['explanation'],
                      audioBase64: null,
                      translationAudioBase64:
                          message['explanation_translation_audio_base64'],
                      isNativeLanguage: false,
                      translation: message['explanation_translation'],
                    ),
                  if (message.containsKey('feedback'))
                    _buildBubbleSection(
                      title: 'Feedback',
                      text: message['feedback'],
                      audioBase64: message['feedback_audio_base64'],
                      isNativeLanguage: false,
                    ),
                  if (message.containsKey('question'))
                    _buildBubbleSection(
                      title: 'Question',
                      text: message['question'],
                      audioBase64: message['question_audio_base64'],
                      translationAudioBase64:
                          message['question_translation_audio_base64'],
                      isNativeLanguage: false,
                      translation: message['question_translation'],
                    ),
                  if (message.containsKey('hint'))
                    if (message['hintVisible'] == true)
                      _buildBubbleSection(
                        title: 'Hint',
                        text: message['hint'],
                        audioBase64: null,
                        isNativeLanguage: false,
                      )
                    else
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              message['hintVisible'] = true;
                            });
                          },
                          child: Icon(
                            Icons.lightbulb_outline,
                            color: nebulaTeal,
                            size: 30,
                          ),
                        ),
                      ),
                ],
                if (message.containsKey('conclusion'))
                  _buildBubbleSection(
                    title: 'Conclusion',
                    text: message['conclusion'],
                    audioBase64: null,
                    isNativeLanguage: false,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCorrectionSection({
    required Map<String, dynamic> correctionData,
  }) {
    String youSaid = correctionData['you_said'] ?? 'N/A';
    String corrected = correctionData['corrected'] ?? 'N/A';
    String youSaidTranslation = correctionData['you_said_translation'] ?? '';
    String correctedTranslation = correctionData['corrected_translation'] ?? '';

    return Container(
      margin: const EdgeInsets.only(bottom: 12.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: nebulaTeal.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Correction',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: nebulaTeal,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'You said: "$youSaid"',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (youSaidTranslation.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 4.0),
              child: Text(
                '-> $youSaidTranslation',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.white70,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          const SizedBox(height: 8),
          Text(
            'Corrected: "$corrected"',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (correctedTranslation.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 4.0),
              child: Text(
                '-> $correctedTranslation',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.white70,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildBubbleSection({
    required String title,
    required String text,
    String? audioBase64,
    String? translationAudioBase64,
    required bool isNativeLanguage,
    String? translation,
  }) {
    final translationKey = "${messages.length - 1}_$title";
    bool isTranslationVisible = _translationVisibility[translationKey] ?? false;
    bool isCurrentlyPlayingThis =
        _isPlaying && _currentlyPlayingId == "${title}_${messages.length - 1}";

    return Container(
      margin: EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: nebulaTeal.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                title,
                style: TextStyle(
                  color: nebulaTeal,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              Spacer(),
              if (translation != null)
                IconButton(
                  icon: Icon(
                    isTranslationVisible
                        ? Icons.visibility_off
                        : Icons.translate,
                    color: nebulaTeal,
                  ),
                  onPressed: () {
                    setState(() {
                      _translationVisibility[translationKey] =
                          !isTranslationVisible;
                    });
                    if (!isTranslationVisible) {
                      final audioToPlay = (translationAudioBase64 != null &&
                              translationAudioBase64.isNotEmpty)
                          ? translationAudioBase64
                          : (audioBase64 != null && audioBase64.isNotEmpty)
                              ? audioBase64
                              : null;
                      if (audioToPlay != null) {
                        _playAudio(audioToPlay,
                            "${title}_translation_${messages.length - 1}");
                      }
                    }
                  },
                ),
              if (audioBase64 != null && audioBase64.isNotEmpty)
                IconButton(
                  icon: Icon(
                    isCurrentlyPlayingThis ? Icons.stop : Icons.play_arrow,
                    color: nebulaTeal,
                  ),
                  onPressed: isCurrentlyPlayingThis
                      ? () async {
                          await audioPlayer.stop();
                          if (mounted) {
                            setState(() {
                              _isPlaying = false;
                              _currentlyPlayingId = null;
                            });
                          }
                        }
                      : () => _playAudio(
                          audioBase64, "${title}_${messages.length - 1}"),
                ),
            ],
          ),
          SizedBox(height: 8),
          Text(
            text,
            style: TextStyle(
              color: Colors.white,
              fontSize: 15,
            ),
          ),
          if (isTranslationVisible && translation != null)
            Padding(
              padding: EdgeInsets.only(top: 8),
              child: Text(
                translation,
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.themeTopic,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: 18,
              ),
            ),
            Text(
              '${widget.userRole} ↔ ${widget.botRole}',
              style: TextStyle(
                fontSize: 14,
                color: Colors.white70,
              ),
            ),
          ],
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                nebulaPurple.withOpacity(0.8),
                Colors.transparent,
              ],
            ),
          ),
        ),
        iconTheme: IconThemeData(color: Colors.white),
      ),
      body: Stack(
        children: [
          // Static Nebula Background
          Container(
            decoration: BoxDecoration(
              gradient: RadialGradient(
                center: Alignment.topRight,
                radius: 1.5,
                colors: [
                  cosmicBackground,
                  nebulaPurple.withOpacity(0.7),
                  nebulaBlue.withOpacity(0.5),
                ],
                stops: [0.1, 0.5, 1.0],
              ),
            ),
            child: CustomPaint(
              painter: _StaticNebulaPainter(),
            ),
          ),

          // Content
          Column(
            children: [
              Expanded(
                child: ListView.builder(
                  padding: EdgeInsets.only(
                    top: MediaQuery.of(context).padding.top + 70,
                    bottom: 100,
                  ),
                  itemCount: messages.length,
                  itemBuilder: (context, index) => _buildMessageCard(messages[index], index),
                ),
              ),
              
              // Recording Section
              _buildRecordingSection(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRecordingSection() {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.transparent,
            nebulaPurple.withOpacity(0.7),
          ],
        ),
      ),
      child: Column(
        children: [
          if (_statusMessage != null)
            Text(
              _statusMessage!,
              style: TextStyle(
                color: nebulaTeal,
                fontSize: 14,
              ),
            ),
          SizedBox(height: 12),
          GestureDetector(
            onTap: () async {
              if (_isRecording) {
                await _stopRecording();
              } else {
                await _startRecording();
              }
            },
            child: Container(
              width: _isRecording ? 80 : 60,
              height: _isRecording ? 80 : 60,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: RadialGradient(
                  colors: _isRecording
                      ? [Colors.red, Colors.redAccent]
                      : [nebulaTeal, nebulaBlue],
                ),
                boxShadow: [
                  BoxShadow(
                    color: _isRecording
                        ? Colors.red.withOpacity(0.7)
                        : nebulaTeal.withOpacity(0.5),
                    blurRadius: _isRecording ? 20 : 15,
                  ),
                ],
              ),
              child: Icon(
                _isRecording ? Icons.stop : Icons.mic,
                color: Colors.white,
                size: _isRecording ? 36 : 30,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    audioPlayer.dispose();
    _audioRecorder.dispose();
    super.dispose();
  }
}

class _StaticNebulaPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final paint = Paint()
      ..shader = RadialGradient(
        colors: [
          nebulaPink.withOpacity(0.1),
          nebulaBlue.withOpacity(0.05),
          Colors.transparent,
        ],
        stops: [0.1, 0.5, 1.0],
      ).createShader(Rect.fromCircle(center: center, radius: size.width * 0.7));

    // Draw nebula core
    canvas.drawCircle(center, size.width * 0.4, paint);

    // Draw stars
    final random = Random(42); // Fixed seed for consistent star pattern
    final starPaint = Paint()..color = starWhite;
    
    for (int i = 0; i < 200; i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      final radius = random.nextDouble() * 1.5 + 0.5;
      final opacity = random.nextDouble() * 0.5 + 0.3;
      
      canvas.drawCircle(
        Offset(x, y),
        radius,
        starPaint..color = starWhite.withOpacity(opacity),
      );
    }

    // Draw cosmic dust
    final dustPaint = Paint()
      ..color = nebulaTeal.withOpacity(0.03)
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, 10);
    
    for (int i = 0; i < 50; i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      final radius = random.nextDouble() * 30 + 10;
      canvas.drawCircle(Offset(x, y), radius, dustPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}