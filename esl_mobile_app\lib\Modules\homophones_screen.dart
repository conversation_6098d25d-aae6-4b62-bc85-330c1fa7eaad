import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:confetti/confetti.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/animation.dart';
import 'package:e_sasthra/screens/explore_screen.dart';

// Light theme colors only
const Color primaryColor = Color(0xFF4DB6AC); // Teal
const Color secondaryColor = Color(0xFF80DEEA); // Light blue
const Color accentColor = Color(0xFFAED581); // Light green
const Color textColor = Color(0xFF424242); // Dark gray
const Color backgroundColor = Color(0xFFFAFAFA); // Light background

class QuizQuestion {
  final int id;
  final String text;
  final List<String> options;
  final String correctAnswer;
  final String? image;

  QuizQuestion({
    required this.id,
    required this.text,
    required this.options,
    required this.correctAnswer,
    this.image,
  });

  factory QuizQuestion.fromJson(Map<String, dynamic> json) {
    String? image;
    if (json.containsKey('image')) {
      image = json['image'];
      if (image != null && image.startsWith('/')) {
        image = image.substring(1);
      }
    }
    return QuizQuestion(
      id: json['id'],
      text: json['text'],
      options: List<String>.from(json['options']),
      correctAnswer: json['correct_answer'],
      image: image,
    );
  }
}

class HomophonesScreen extends StatefulWidget {
  const HomophonesScreen({super.key});

  @override
  _HomophonesScreenState createState() => _HomophonesScreenState();
}

class _HomophonesScreenState extends State<HomophonesScreen>
    with SingleTickerProviderStateMixin {
  bool _isAudioPlaying = false;
  int _currentQuestion = 0;
  String? _answerSelected;
  String _feedback = '';
  String _correctAnswer = '';
  bool _quizFinished = false;
  List<QuizQuestion> _quizData = [];
  bool _loading = true;
  bool _canReplayAudio = false;
  bool _answerSubmitted = false;
  final List<bool> _results = [];
  bool _showInstructions = true;

  late FlutterTts _flutterTts;
  late ConfettiController _confettiController;
  late AudioPlayer _audioPlayer;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<Color?> _colorAnimation;
  double _scoreOpacity = 0.0;

  @override
  void initState() {
    super.initState();
    _initTts();
    _initAudioPlayer();
    _confettiController =
        ConfettiController(duration: const Duration(seconds: 1));
    _loadQuizData();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOutBack,
      ),
    );

    _scaleAnimation = Tween<double>(begin: 0.7, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.elasticOut,
      ),
    );

    _colorAnimation = ColorTween(
      begin: primaryColor.withOpacity(0.3),
      end: primaryColor,
    ).animate(_animationController);

    _animationController.forward();
  }

  Future<void> _initTts() async {
    _flutterTts = FlutterTts();
    await _flutterTts.setLanguage("en-US");
    await _flutterTts.setPitch(1.0);
    await _flutterTts.setSpeechRate(0.5);
    _flutterTts.setCompletionHandler(() {
      setState(() {
        _isAudioPlaying = false;
        _canReplayAudio = true;
      });
    });
  }

  void _initAudioPlayer() {
    _audioPlayer = AudioPlayer();
  }

  Future<void> _loadQuizData() async {
    try {
      String jsonString = await rootBundle.loadString('assets/quizData.json');
      Map<String, dynamic> data = jsonDecode(jsonString);
      List<dynamic> questionsJson = data['questions'];
      List<QuizQuestion> allQuestions =
          questionsJson.map((json) => QuizQuestion.fromJson(json)).toList();
      allQuestions.shuffle();
      List<QuizQuestion> selectedQuestions = allQuestions.sublist(
          0, allQuestions.length > 10 ? 10 : allQuestions.length);
      setState(() {
        _quizData = selectedQuestions;
        _loading = false;
      });
      if (_quizData.isNotEmpty && !_showInstructions) {
        _playTextAsAudio(_quizData[_currentQuestion].text);
      }
    } catch (e) {
      print('Error loading quiz data: $e');
      setState(() {
        _loading = false;
      });
    }
  }

  Future<void> _playTextAsAudio(String text) async {
    setState(() {
      _isAudioPlaying = true;
    });
    await _flutterTts.speak(text);
  }

  Future<void> _playApplauseSound() async {
    await _audioPlayer.play(AssetSource('sounds/applause-180037.mp3'));
  }

  Future<void> _playCorrectSound() async {
    await _audioPlayer.play(AssetSource('sounds/correct_answer.mp3'));
  }

  Future<void> _playWrongSound() async {
    await _audioPlayer.play(AssetSource('sounds/wrong_answer.mp3'));
  }

  @override
  void dispose() {
    _flutterTts.stop();
    _confettiController.dispose();
    _audioPlayer.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Widget _buildFeedback() {
    if (_feedback.isEmpty) return const SizedBox.shrink();

    final isCorrect = _feedback == 'Correct answer!';
    final iconData = isCorrect ? Icons.check_circle : Icons.cancel;
    final color = isCorrect ? Colors.green : Colors.red;

    return SlideTransition(
      position: Tween<Offset>(begin: const Offset(0, 1), end: Offset.zero)
          .animate(CurvedAnimation(
        parent: _animationController,
        curve: Curves.bounceOut,
      )),
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(16.0),
            border: Border.all(color: color, width: 2),
            boxShadow: [
              BoxShadow(
                color: color.withOpacity(0.2),
                blurRadius: 10,
                spreadRadius: 2,
              ),
            ],
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(iconData, color: color, size: 32),
                  const SizedBox(width: 12),
                  Text(
                    _feedback,
                    style: TextStyle(
                      color: color,
                      fontWeight: FontWeight.bold,
                      fontSize: 22,
                      shadows: [
                        Shadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              if (!isCorrect)
                Padding(
                  padding: const EdgeInsets.only(top: 12.0),
                  child: Text(
                    'Correct Answer: $_correctAnswer',
                    style: TextStyle(
                      fontSize: 18,
                      color: textColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _finishQuiz() async {
    setState(() {
      _quizFinished = true;
    });
    _confettiController.play();
    await _playApplauseSound();

    Future.delayed(const Duration(milliseconds: 100), () {
      setState(() {
        _scoreOpacity = 1.0;
      });
    });
  }

  Widget _buildFinalScore() {
    int correctCount = _results.where((result) => result).length;
    int totalQuestions = _quizData.length;
    double percentage = (correctCount / totalQuestions) * 100;

    String feedback;
    Color feedbackColor;
    if (percentage >= 80) {
      feedback = 'Excellent!';
      feedbackColor = Colors.green;
    } else if (percentage >= 50) {
      feedback = 'Good Job!';
      feedbackColor = const Color.fromARGB(255, 243, 243, 33);
    } else {
      feedback = 'Keep Practicing!';
      feedbackColor = Colors.orange;
    }

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: textColor),
          onPressed: () {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (context) => const ExploreScreen()),
            );
          },
        ),
        title: Text(
          'Homophones Quiz',
          style: TextStyle(
            color: textColor,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: Stack(
        children: [
          Align(
            alignment: Alignment.topCenter,
            child: ConfettiWidget(
              confettiController: _confettiController,
              blastDirectionality: BlastDirectionality.explosive,
              emissionFrequency: 0.05,
              numberOfParticles: 1,
              minBlastForce: 5,
              maxBlastForce: 20,
              gravity: 0.1,
              shouldLoop: true,
              colors: const [
                Colors.purple,
                Colors.blue,
                Colors.pink,
                Colors.orange,
                Colors.teal,
                Colors.yellow,
              ],
            ),
          ),
          Center(
            child: AnimatedOpacity(
              opacity: _scoreOpacity,
              duration: const Duration(seconds: 1),
              curve: Curves.easeInOutBack,
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 24.0),
                padding: const EdgeInsets.all(24.0),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      const Color(0xFFE0F7FA).withOpacity(0.95),
                      const Color(0xFFB2EBF2).withOpacity(0.95),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(20.0),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 20,
                      spreadRadius: 5,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      percentage >= 80
                          ? Icons.celebration
                          : percentage >= 50
                              ? Icons.thumb_up
                              : Icons.school,
                      size: 80,
                      color: feedbackColor,
                    ),
                    const SizedBox(height: 20),
                    Text(
                      feedback,
                      style: TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: feedbackColor,
                        shadows: [
                          Shadow(
                            color: Colors.black.withOpacity(0.3),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 20),
                    Text(
                      'Your Score: $correctCount / $totalQuestions',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: textColor,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      '${percentage.toStringAsFixed(1)}% Correct',
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.w600,
                        color: textColor.withOpacity(0.8),
                      ),
                    ),
                    const SizedBox(height: 24),
                    ScaleTransition(
                      scale: Tween<double>(begin: 0.9, end: 1.0).animate(
                        CurvedAnimation(
                          parent: _animationController,
                          curve: Curves.easeInOut,
                        ),
                      ),
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.pushReplacement(
                            context,
                            MaterialPageRoute(
                                builder: (context) => const ExploreScreen()),
                          );
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: primaryColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                              vertical: 16.0, horizontal: 32.0),
                          textStyle: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 8,
                          shadowColor: primaryColor.withOpacity(0.5),
                        ),
                        child: const Text('Back to Explore'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptionButton(String option, int index) {
    final isSelected = _answerSelected == option;
    final isCorrect = option == _correctAnswer;
    final isWrongSelected = _answerSubmitted && isSelected && !isCorrect;

    Color backgroundColor;
    Color textColor;

    if (_answerSubmitted) {
      if (isCorrect) {
        backgroundColor = Colors.green.withOpacity(0.8);
        textColor = Colors.white;
      } else if (isWrongSelected) {
        backgroundColor = Colors.red.withOpacity(0.8);
        textColor = Colors.white;
      } else {
        backgroundColor = Colors.grey.withOpacity(0.1);
        textColor = Colors.black87.withOpacity(0.9);
      }
    } else {
      backgroundColor = isSelected
          ? primaryColor.withOpacity(0.8)
          : Colors.grey.withOpacity(0.05);
      textColor = isSelected ? Colors.white : Colors.black87;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: backgroundColor,
          border: Border.all(
            color: isSelected ? primaryColor : Colors.grey.withOpacity(0.2),
            width: 1,
          ),
          boxShadow: [
            if (isSelected && !_answerSubmitted)
              BoxShadow(
                color: primaryColor.withOpacity(0.5),
                blurRadius: 10,
                spreadRadius: 2,
              ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(12),
            onTap: _answerSubmitted
                ? null
                : () {
                    setState(() {
                      _answerSelected = option;
                    });
                    HapticFeedback.lightImpact();
                  },
            child: Padding(
              padding: const EdgeInsets.symmetric(
                vertical: 16.0,
                horizontal: 24.0,
              ),
              child: Row(
                children: [
                  AnimatedSwitcher(
                    duration: const Duration(milliseconds: 300),
                    child: _answerSubmitted
                        ? Icon(
                            isCorrect
                                ? Icons.check_circle
                                : isWrongSelected
                                    ? Icons.cancel
                                    : null,
                            color: Colors.white,
                            size: 24,
                          )
                        : Container(
                            width: 24,
                            height: 24,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: textColor.withOpacity(0.5),
                                width: 2,
                              ),
                            ),
                          ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      option,
                      style: TextStyle(
                        fontSize: 18,
                        color: textColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInstructions() {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: textColor),
          onPressed: () {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (context) => const ExploreScreen()),
            );
          },
        ),
        title: Text(
          'Homophones Quiz',
          style: TextStyle(
            color: textColor,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: Center(
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 24.0),
          padding: const EdgeInsets.all(24.0),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFFE0F7FA).withOpacity(0.95),
                const Color(0xFFB2EBF2).withOpacity(0.95),
              ],
            ),
            borderRadius: BorderRadius.circular(20.0),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 20,
                spreadRadius: 5,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.hearing,
                size: 80,
                color: primaryColor,
              ),
              const SizedBox(height: 20),
              const Text(
                'Homophones Quiz Instructions',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: textColor,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              const Text(
                '1. Listen carefully to the audio played for each question.\n'
                '2. The audio will pronounce a word that sounds the same as other words (homophones).\n'
                '3. Select the correct spelling of the word you hear from the options provided.\n'
                '4. You can replay the audio if needed using the replay button.\n'
                '5. Submit your answer and proceed to the next question.\n'
                '6. At the end, you will see your score and feedback.',
                style: TextStyle(
                  fontSize: 18,
                  color: textColor,
                  height: 1.5,
                ),
                textAlign: TextAlign.left,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _showInstructions = false;
                    if (_quizData.isNotEmpty) {
                      _playTextAsAudio(_quizData[_currentQuestion].text);
                    }
                  });
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                      vertical: 16.0, horizontal: 32.0),
                  textStyle: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 8,
                  shadowColor: primaryColor.withOpacity(0.5),
                ),
                child: const Text('Start Quiz'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuizContent() {
    if (_showInstructions) {
      return _buildInstructions();
    }

    if (_quizFinished) {
      return _buildFinalScore();
    }

    final question = _quizData[_currentQuestion];

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: textColor),
          onPressed: () {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (context) => const ExploreScreen()),
            );
          },
        ),
        title: Text(
          'Homophones Quiz',
          style: TextStyle(
            color: textColor,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: Column(
        children: [
          // Question progress indicator
          Container(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.7),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              'Question ${_currentQuestion + 1} of ${_quizData.length}',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: textColor,
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Question image (if any)
          if (question.image != null)
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 16.0),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: Image.asset(
                  question.image!,
                  fit: BoxFit.cover,
                  height: 150,
                  width: double.infinity,
                ),
              ),
            ),

          // Audio controls only (question text removed)
          Container(
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.7),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  icon: Icon(
                    _isAudioPlaying ? Icons.volume_up : Icons.volume_off,
                    color: textColor,
                  ),
                  onPressed: () {
                    if (!_isAudioPlaying) {
                      _playTextAsAudio(question.text);
                    }
                  },
                ),
                if (_canReplayAudio)
                  TextButton(
                    onPressed: () {
                      setState(() {
                        _canReplayAudio = false;
                      });
                      _playTextAsAudio(question.text);
                    },
                    child: Text(
                      'Replay',
                      style: TextStyle(color: textColor),
                    ),
                  ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // Options list
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              children: question.options
                  .map((option) => _buildOptionButton(
                      option, question.options.indexOf(option)))
                  .toList(),
            ),
          ),

          // Feedback and navigation buttons
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                if (_feedback.isNotEmpty) _buildFeedback(),
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton(
                      onPressed: _answerSelected != null && !_answerSubmitted
                          ? () {
                              setState(() {
                                _correctAnswer = question.correctAnswer;
                                bool isCorrect =
                                    _answerSelected == _correctAnswer;
                                _feedback = isCorrect
                                    ? 'Correct answer!'
                                    : 'Wrong answer!';
                                _results.add(isCorrect);
                                _answerSubmitted = true;

                                if (isCorrect) {
                                  _playCorrectSound();
                                } else {
                                  _playWrongSound();
                                }

                                _animationController.reset();
                                _animationController.forward();
                              });
                            }
                          : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor:
                            _answerSelected != null && !_answerSubmitted
                                ? primaryColor
                                : primaryColor.withOpacity(0.5),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        'Submit',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                    ElevatedButton(
                      onPressed: _answerSubmitted
                          ? () {
                              if (_currentQuestion < _quizData.length - 1) {
                                setState(() {
                                  _currentQuestion++;
                                  _answerSelected = null;
                                  _feedback = '';
                                  _answerSubmitted = false;
                                  _canReplayAudio = false;
                                  _animationController.reset();
                                  _animationController.forward();
                                });
                                _playTextAsAudio(
                                    _quizData[_currentQuestion].text);
                              } else {
                                _finishQuiz();
                              }
                            }
                          : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _answerSubmitted
                            ? secondaryColor
                            : secondaryColor.withOpacity(0.5),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        _currentQuestion < _quizData.length - 1
                            ? 'Next'
                            : 'Finish',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_loading) {
      return Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: IconButton(
            icon: Icon(Icons.arrow_back, color: textColor),
            onPressed: () {
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(builder: (context) => const ExploreScreen()),
              );
            },
          ),
          title: Text(
            'Homophones Quiz',
            style: TextStyle(
              color: textColor,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [const Color(0xFFE0F7FA), const Color(0xFFB2EBF2)],
            ),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(height: 20),
                Text(
                  "Preparing your quiz...",
                  style: TextStyle(
                    color: textColor.withOpacity(0.9),
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 20),
                SizedBox(
                  width: 200,
                  child: LinearProgressIndicator(
                    backgroundColor: Colors.grey.withOpacity(0.2),
                    valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
                    minHeight: 6,
                    borderRadius: BorderRadius.circular(3),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    if (_quizData.isEmpty) {
      return Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: IconButton(
            icon: Icon(Icons.arrow_back, color: textColor),
            onPressed: () {
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(builder: (context) => const ExploreScreen()),
              );
            },
          ),
          title: Text(
            'Homophones Quiz',
            style: TextStyle(
              color: textColor,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [const Color(0xFFE0F7FA), const Color(0xFFB2EBF2)],
            ),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 80,
                  color: Colors.red.withOpacity(0.8),
                ),
                const SizedBox(height: 20),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 40),
                  child: Text(
                    'Could not load quiz questions. Please check your connection and try again.',
                    style: TextStyle(
                      color: textColor.withOpacity(0.9),
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 30),
                ElevatedButton(
                  onPressed: _loadQuizData,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 32, vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return _buildQuizContent();
  }
}