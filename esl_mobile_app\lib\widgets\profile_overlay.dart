import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import '../main.dart'; // Import for ThemeProvider
import '../user_provider.dart';
import '../constants/app_colors.dart'; // Import colors.dart

class ProfileOverlay extends StatefulWidget {
  final Function onProfileUpdated;

  const ProfileOverlay({super.key, required this.onProfileUpdated});

  @override
  State<ProfileOverlay> createState() => _ProfileOverlayState();
}

class _ProfileOverlayState extends State<ProfileOverlay>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  final FlutterSecureStorage _storage = const FlutterSecureStorage();
  File? _pickedImageFile;
  String? _currentImagePath;

  late TextEditingController _firstNameController;
  late TextEditingController _lastNameController;
  late TextEditingController _emailController;
  late TextEditingController _nationalityController;
  late TextEditingController _nativeLanguageController;
  late TextEditingController _levelOfEnglishController;
  late TextEditingController _userNameController;
  late TextEditingController _ageController;

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutBack),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn),
    );

    _animationController.forward();

    final userProvider = Provider.of<UserProvider>(context, listen: false);
    _firstNameController =
        TextEditingController(text: userProvider.firstName ?? '');
    _lastNameController =
        TextEditingController(text: userProvider.lastName ?? '');
    _emailController = TextEditingController(text: userProvider.email ?? '');
    _nationalityController =
        TextEditingController(text: userProvider.nationality ?? '');
    _nativeLanguageController =
        TextEditingController(text: userProvider.nativeLanguage ?? '');
    _levelOfEnglishController =
        TextEditingController(text: userProvider.levelOfEnglish ?? '');
    _userNameController =
        TextEditingController(text: userProvider.userName ?? '');
    _ageController =
        TextEditingController(text: userProvider.age?.toString() ?? '');
    _currentImagePath = userProvider.profilePicture;
  }

  @override
  void dispose() {
    _animationController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _nationalityController.dispose();
    _nativeLanguageController.dispose();
    _levelOfEnglishController.dispose();
    _userNameController.dispose();
    _ageController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      setState(() {
        _pickedImageFile = File(pickedFile.path);
        _currentImagePath = null; // Clear current path if new image is picked
      });
    }
  }

  Future<String?> _saveImageLocally(Uint8List imageBytes) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final filePath = '${directory.path}/profile_$timestamp.png';
      final file = File(filePath);
      await file.writeAsBytes(imageBytes);
      print("Profile image saved to: $filePath");
      return filePath;
    } catch (e) {
      print("Error saving image: $e");
      return null;
    }
  }

  Future<void> _updateProfile() async {
    if (!mounted) return;
    setState(() {
      _isLoading = true;
    });

    final token = await _storage.read(key: 'access_token');
    if (token == null) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
            content: Text('Authentication error. Please log in again.')));
        Navigator.pop(context);
      }
      return;
    }

    String? base64Image;
    String? newImagePath = _pickedImageFile?.path;

    if (_pickedImageFile != null) {
      try {
        final bytes = await _pickedImageFile!.readAsBytes();
        base64Image = base64Encode(bytes);
      } catch (e) {
        print("Error reading/encoding image: $e");
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Error processing image.')));
        }
        setState(() {
          _isLoading = false;
        });
        return;
      }
    }

    final profileData = {
      'first_name': _firstNameController.text.trim(),
      'last_name': _lastNameController.text.trim(),
      'email': _emailController.text.trim(),
      'nationality': _nationalityController.text.trim(),
      'native_language': _nativeLanguageController.text.trim(),
      'level_of_english': _levelOfEnglishController.text.trim(),
      'user_name': _userNameController.text.trim(),
      'age': int.tryParse(_ageController.text.trim()),
      if (base64Image != null) 'profile_picture': base64Image,
    };
    profileData.removeWhere(
        (key, value) => value == null || (value is String && value.isEmpty));

    try {
      final response = await http
          .put(
            Uri.parse('https://talktoai.in/update-profile'),
            headers: {
              'Authorization': 'Bearer $token',
              'Content-Type': 'application/json; charset=utf-8'
            },
            body: jsonEncode(profileData),
          )
          .timeout(const Duration(seconds: 15));

      if (response.statusCode == 200) {
        print("Profile updated successfully.");
        if (mounted) {
          final userProvider =
              Provider.of<UserProvider>(context, listen: false);
          await userProvider.setUserDetails(
            userId: userProvider.userId,
            phoneNumber: userProvider.phoneNumber,
            firstName: _firstNameController.text.trim(),
            lastName: _lastNameController.text.trim(),
            email: _emailController.text.trim(),
            nationality: _nationalityController.text.trim(),
            nativeLanguage: _nativeLanguageController.text.trim(),
            levelOfEnglish: _levelOfEnglishController.text.trim(),
            userName: _userNameController.text.trim(),
            age: int.tryParse(_ageController.text.trim()),
            profilePicture: newImagePath ?? _currentImagePath,
          );
          ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Profile updated successfully!')));
          widget
              .onProfileUpdated(); // Call callback to re-fetch profile in home screen
          Navigator.pop(context); // Close the overlay
        }
      } else {
        print(
            "Update profile failed. Status: ${response.statusCode}, Body: ${response.body}");
        String errorMessage =
            'Failed to update profile (status ${response.statusCode}).';
        try {
          final errorBody = jsonDecode(utf8.decode(response.bodyBytes));
          if (errorBody['detail'] != null) {
            errorMessage = "Update failed: ${errorBody['detail']}";
          }
        } catch (_) {}
        if (mounted) {
          ScaffoldMessenger.of(context)
              .showSnackBar(SnackBar(content: Text(errorMessage)));
        }
      }
    } on TimeoutException catch (_) {
      print('Network timeout updating profile.');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
            content: Text("Network timeout. Check connection.")));
      }
    } catch (e) {
      print('Error updating profile: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
            content: Text(
                "Could not update profile: ${e.toString().split(':').last.trim()}")));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final userProvider = Provider.of<UserProvider>(context);
    final themeProvider = Provider.of<ThemeProvider>(context);

    return FadeTransition(
      opacity: _fadeAnimation,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: const EdgeInsets.all(20),
          child: Stack(
            children: [
              // Theme-aware Background with Stars
              Container(
                decoration: BoxDecoration(
                  color: themeProvider.isDarkMode
                      ? AppColors.darkBackground
                      : AppColors.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primary.withOpacity(0.3),
                      blurRadius: 20,
                      spreadRadius: 5,
                    ),
                  ],
                ),
                child: CustomPaint(
                  //painter: StarsPainter(),
                  size: Size(MediaQuery.of(context).size.width,
                      MediaQuery.of(context).size.height),
                ),
              ),
              // Profile Content
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: themeProvider.isDarkMode
                      ? AppColors.primary.withOpacity(0.6)
                      : AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                      color: AppColors.primary.withOpacity(0.5), width: 2),
                ),
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Align(
                        alignment: Alignment.topRight,
                        child: IconButton(
                          icon: Icon(
                            Icons.close,
                            color: themeProvider.isDarkMode
                                ? Colors.yellow
                                : AppColors.primary,
                          ),
                          onPressed: () => Navigator.pop(context),
                        ),
                      ),
                      GestureDetector(
                        onTap: _pickImage,
                        child: CircleAvatar(
                          radius: 60,
                          backgroundColor: AppColors.primary,
                          backgroundImage: _pickedImageFile != null
                              ? FileImage(_pickedImageFile!)
                              : (_currentImagePath != null &&
                                      File(_currentImagePath!).existsSync())
                                  ? FileImage(File(_currentImagePath!))
                                  : null,
                          child: (_pickedImageFile == null &&
                                  (_currentImagePath == null ||
                                      !File(_currentImagePath!).existsSync()))
                              ? Icon(
                                  Icons.add_a_photo,
                                  size: 60,
                                  color: themeProvider.isDarkMode
                                      ? Colors.yellow
                                      : AppColors.white,
                                )
                              : null,
                        ),
                      ),
                      const SizedBox(height: 20),
                      _buildProfileTextField(
                          _firstNameController, 'First Name', Icons.person),
                      _buildProfileTextField(_lastNameController, 'Last Name',
                          Icons.person_outline),
                      _buildProfileTextField(
                          _emailController, 'Email', Icons.email),
                      _buildProfileTextField(
                          _nationalityController, 'Nationality', Icons.flag),
                      _buildProfileTextField(_nativeLanguageController,
                          'Native Language', Icons.language),
                      _buildProfileTextField(_levelOfEnglishController,
                          'Level of English', Icons.school),
                      _buildProfileTextField(_userNameController, 'User Name',
                          Icons.account_circle),
                      _buildProfileTextField(_ageController, 'Age', Icons.cake,
                          keyboardType: TextInputType.number),
                      const SizedBox(height: 30),
                      _isLoading
                          ? const CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation<Color>(
                                  AppColors.primary),
                            )
                          : ElevatedButton.icon(
                              onPressed: _updateProfile,
                              icon: const Icon(Icons.save, color: Colors.white),
                              label: const Text('Save Profile',
                                  style: TextStyle(color: Colors.white)),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppColors.primary,
                                foregroundColor: Colors.yellow,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 30, vertical: 15),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(30),
                                ),
                              ),
                            ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileTextField(
      TextEditingController controller, String label, IconData icon,
      {TextInputType keyboardType = TextInputType.text}) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: TextField(
        controller: controller,
        keyboardType: keyboardType,
        style: TextStyle(
            color: themeProvider.isDarkMode ? AppColors.white : AppColors.black),
        decoration: InputDecoration(
          labelText: label,
          labelStyle: TextStyle(
              color: themeProvider.isDarkMode
                  ? AppColors.white.withOpacity(0.7)
                  : AppColors.grey600),
          prefixIcon: Icon(icon, color: AppColors.primary),
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(color: AppColors.primary.withOpacity(0.5)),
            borderRadius: BorderRadius.circular(10),
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: const BorderSide(color: Colors.yellow, width: 2),
            borderRadius: BorderRadius.circular(10),
          ),
          filled: true,
          fillColor: themeProvider.isDarkMode
              ? AppColors.primary.withOpacity(0.1)
              : AppColors.grey200,
        ),
      ),
    );
  }
}