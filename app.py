import os
import random
import datetime
import requests
import socket
from flask import Flask, request, jsonify
from pymongo import MongoClient, ReturnDocument
from pymongo.errors import ConnectionFailure
import jwt
from io import BytesIO
from base64 import b64decode, b64encode

# --- SMS Gateway Hub Credentials ---
SMSGATEWAYHUB_API_KEY = "V2UAWhucjEesM8TTo5E34g"
SMSGATEWAYHUB_SENDER_ID = "DSAIS2"
ENTITY_ID = "1701174296617710373"
DLT_TEMPLATE_ID = "1707174315682876645"
REGISTERED_TEMPLATE_TEXT = "Dear Customer, {#var#} is your Dsais account verification code - DSAIS"

# --- MongoDB Setup ---
MONGO_URI = "**********************************************************************************************************************"

try:
    mongo_client = MongoClient(MONGO_URI)
    db = mongo_client.get_default_database()
    otp_collection = db['otps']
    user_collection = db['users']
    counters_collection = db['counters']
    assessment_collection = db['assessments']
    otp_collection.create_index("expires_at", expireAfterSeconds=0)
except ConnectionFailure as e:
    print("Could not connect to MongoDB:", e)
    exit(1)

# --- Initialize Flask App ---
app = Flask(__name__)

# Secret keys for JWT (use secure keys in production)
ACCESS_TOKEN_SECRET = "2350800063df4f4a3219ecdaa33f4406"
REFRESH_TOKEN_SECRET = "e46b80f33be8c6e1834c03264383e24a"

# Constants for error messages
BEARER_PREFIX = "Bearer "
AUTH_HEADER_ERROR = "Authorization header missing or invalid"
TOKEN_EXPIRED_ERROR = "Access token has expired"
INVALID_TOKEN_ERROR = "Invalid token"

def get_network_ip():
    """Retrieve the network IP address."""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception:
        return "Could not determine network IP"

def generate_otp():
    """Generate a 6-digit OTP."""
    return str(random.randint(100000, 999999))

def format_phone_number(phone_number):
    """Ensure the phone number is in the correct format."""
    phone_number = phone_number.replace("+", "").strip()
    if len(phone_number) == 10:
        phone_number = "91" + phone_number
    return phone_number

def send_sms_via_smsgatewayhub(phone_number, otp):
    """Send SMS using SMS Gateway Hub API."""
    url = "https://www.smsgatewayhub.com/api/mt/SendSMS"
    message = REGISTERED_TEMPLATE_TEXT.replace("{#var#}", otp)

    params = {
        "APIKey": SMSGATEWAYHUB_API_KEY,
        "senderid": SMSGATEWAYHUB_SENDER_ID,
        "channel": 2,
        "DCS": 0,
        "flashsms": 0,
        "number": phone_number,
        "text": message,
        "route": "clickhere",
        "EntityId": ENTITY_ID,
        "dlttemplateid": DLT_TEMPLATE_ID
    }
    try:
        response = requests.get(url, params=params, timeout=10)
        response_data = response.json()
        print("SMS Gateway Hub Response:", response_data)
        return response_data
    except Exception as e:
        print("Error sending SMS:", str(e))
        return {"ErrorCode": "999", "ErrorMessage": str(e)}

def get_next_user_id():
    """Generate the next unique user_id."""
    counter = counters_collection.find_one_and_update(
        {"_id": "user_id"},
        {"$inc": {"sequence_value": 1}},
        upsert=True,
        return_document=ReturnDocument.AFTER
    )
    return counter["sequence_value"]

@app.route('/send-otp', methods=['POST'])
def send_otp():
    data = request.json
    phone_number = data.get("phone_number")

    if not phone_number:
        return jsonify({"error": "Phone number is required"}), 400

    phone_number = format_phone_number(phone_number)

    otp = generate_otp()
    now = datetime.datetime.now(tz=datetime.timezone.utc)
    expires_at = now + datetime.timedelta(minutes=5)

    otp_collection.update_one(
        {"phone_number": phone_number},
        {"$set": {"otp": otp, "created_at": now, "expires_at": expires_at}},
        upsert=True
    )

    response = send_sms_via_smsgatewayhub(phone_number, otp)

    if response.get("ErrorCode") == "000":
        return jsonify({"message": "OTP sent successfully"}), 200
    else:
        return jsonify({"error": "Failed to send OTP", "details": response.get("ErrorMessage", "Unknown error")}), 500

@app.route('/verify-otp', methods=['POST'])
def verify_otp():
    data = request.json
    phone_number = data.get("phone_number")
    user_otp = data.get("otp")

    if not phone_number or not user_otp:
        return jsonify({"error": "Phone number and OTP are required"}), 400

    phone_number = format_phone_number(phone_number)

    record = otp_collection.find_one({"phone_number": phone_number})
    if record:
        now = datetime.datetime.now(tz=datetime.timezone.utc)
        expires_at = record["expires_at"].replace(tzinfo=datetime.timezone.utc)

        if now > expires_at:
            otp_collection.delete_one({"phone_number": phone_number})
            return jsonify({"error": "OTP has expired"}), 400

        if record["otp"] == user_otp:
            access_token = jwt.encode({
                'phone_number': phone_number,
                'exp': datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(hours=24)
            }, ACCESS_TOKEN_SECRET, algorithm='HS256')

            refresh_token = jwt.encode({
                'phone_number': phone_number,
                'exp': datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(days=30)
            }, REFRESH_TOKEN_SECRET, algorithm='HS256')

            user = user_collection.find_one({"phone_number": phone_number})
            if not user:
                user_id = get_next_user_id()
                user_collection.insert_one({
                    "user_id": user_id,
                    "phone_number": phone_number,
                    "access_token": access_token,
                    "refresh_token": refresh_token,
                    "first_name": None,
                    "last_name": None,
                    "email": None,
                    "nationality": None,
                    "native_language": None,
                    "level_of_english": None,
                    "user_name": None,
                    "age": None,
                    "profile_picture": None,
                    "profession": None,
                    "school_class": None,
                    "reason_for_learning": None,
                    "assessment_completed": False
                })
            else:
                user_collection.update_one(
                    {"phone_number": phone_number},
                    {"$set": {"access_token": access_token, "refresh_token": refresh_token}}
                )

            otp_collection.delete_one({"phone_number": phone_number})
            return jsonify({
                "message": "OTP verified successfully",
                "access_token": access_token,
                "refresh_token": refresh_token
            }), 200
        else:
            return jsonify({"error": "Invalid OTP"}), 400
    else:
        return jsonify({"error": "No OTP found for this phone number"}), 400

@app.route('/refresh-token', methods=['POST'])
def refresh_token():
    data = request.json
    refresh_token_value = data.get("refresh_token")

    if not refresh_token_value:
        return jsonify({"error": "Refresh token is required"}), 400

    try:
        decoded_token = jwt.decode(refresh_token_value, REFRESH_TOKEN_SECRET, algorithms=['HS256'])
        phone_number = decoded_token['phone_number']
        user = user_collection.find_one({"phone_number": phone_number, "refresh_token": refresh_token_value})
        if user:
            new_access_token = jwt.encode({
                'phone_number': phone_number,
                'exp': datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(hours=24)
            }, ACCESS_TOKEN_SECRET, algorithm='HS256')

            user_collection.update_one(
                {"phone_number": phone_number},
                {"$set": {"access_token": new_access_token}}
            )

            return jsonify({"access_token": new_access_token}), 200
        else:
            return jsonify({"error": "Invalid refresh token"}), 401
    except jwt.ExpiredSignatureError:
        return jsonify({"error": "Refresh token has expired"}), 401
    except jwt.DecodeError:
        return jsonify({"error": "Invalid refresh token"}), 401

@app.route('/user-profile', methods=['GET'])
def get_user_profile():
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith(BEARER_PREFIX):
        return jsonify({"error": AUTH_HEADER_ERROR}), 401
    token = auth_header.split(' ')[1]

    try:
        decoded_token = jwt.decode(token, ACCESS_TOKEN_SECRET, algorithms=['HS256'])
        phone_number = decoded_token['phone_number']
        user = user_collection.find_one({"phone_number": phone_number, "access_token": token})
        if user:
            profile_data = {
                "user_id": user.get("user_id"),
                "phone_number": user.get("phone_number"),
                "first_name": user.get("first_name"),
                "last_name": user.get("last_name"),
                "email": user.get("email"),
                "nationality": user.get("nationality"),
                "native_language": user.get("native_language"),
                "level_of_english": user.get("level_of_english"),
                "user_name": user.get("user_name"),
                "age": user.get("age"),
                "profession": user.get("profession"),
                "school_class": user.get("school_class"),
                "reason_for_learning": user.get("reason_for_learning"),
                "assessment_completed": user.get("assessment_completed", False)
            }
            if user.get("profile_picture"):
                profile_data["profile_picture"] = b64encode(user["profile_picture"]).decode('utf-8')
            return jsonify(profile_data), 200
        else:
            return jsonify({"error": "User not found or token mismatch"}), 404
    except jwt.ExpiredSignatureError:
        return jsonify({"error": TOKEN_EXPIRED_ERROR}), 401
    except jwt.DecodeError:
        return jsonify({"error": INVALID_TOKEN_ERROR}), 401

@app.route('/update-profile', methods=['PUT'])
def update_user_profile():
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith(BEARER_PREFIX):
        return jsonify({"error": AUTH_HEADER_ERROR}), 401
    token = auth_header.split(' ')[1]

    try:
        decoded_token = jwt.decode(token, ACCESS_TOKEN_SECRET, algorithms=['HS256'])
        phone_number = decoded_token['phone_number']
        data = request.json
        update_data = {
            "first_name": data.get("first_name"),
            "last_name": data.get("last_name"),
            "email": data.get("email"),
            "nationality": data.get("nationality"),
            "native_language": data.get("native_language"),
            "level_of_english": data.get("level_of_english"),
            "user_name": data.get("user_name"),
            "age": data.get("age"),
            "profession": data.get("profession"),
            "school_class": data.get("school_class"),
            "reason_for_learning": data.get("reason_for_learning")
        }
        if data.get("profile_picture"):
            # Decode base64 image and store as binary
            image_data = b64decode(data["profile_picture"])
            update_data["profile_picture"] = image_data

        user_collection.update_one(
            {"phone_number": phone_number},
            {"$set": update_data}
        )
        return jsonify({"message": "Profile updated successfully"}), 200
    except jwt.ExpiredSignatureError:
        return jsonify({"error": TOKEN_EXPIRED_ERROR}),401
    except jwt.DecodeError:
        return jsonify({"error": INVALID_TOKEN_ERROR}), 401
    except Exception as e:
        return jsonify({"error": f"Error updating profile: {str(e)}"}), 500

@app.route('/clear-session', methods=['POST'])
def clear_session():
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith(BEARER_PREFIX):
        return jsonify({"error": AUTH_HEADER_ERROR}), 401
    token = auth_header.split(' ')[1]

    try:
        decoded_token = jwt.decode(token, ACCESS_TOKEN_SECRET, algorithms=['HS256'])
        phone_number = decoded_token['phone_number']
        user_collection.update_one(
            {"phone_number": phone_number},
            {"$unset": {"access_token": "", "refresh_token": ""}}
        )
        return jsonify({"message": "Session cleared successfully"}), 200
    except jwt.ExpiredSignatureError:
        return jsonify({"error": TOKEN_EXPIRED_ERROR}), 401
    except jwt.DecodeError:
        return jsonify({"error": INVALID_TOKEN_ERROR}), 401

@app.route('/save-assessment-result', methods=['POST'])
def save_assessment_result():
    token = request.headers.get('Authorization')
    if not token or not token.startswith('Bearer '):
        return jsonify({"error": "Authorization token required"}), 401

    token = token.split(' ')[1]

    try:
        decoded_token = jwt.decode(token, ACCESS_TOKEN_SECRET, algorithms=['HS256'])
        phone_number = decoded_token['phone_number']
        user = user_collection.find_one({"phone_number": phone_number, "access_token": token})

        if not user:
            return jsonify({"error": "Invalid token"}), 401

        data = request.json
        if not data:
            return jsonify({"error": "Assessment result data required"}), 400

        # Add timestamp and user info
        assessment_result = {
            "user_id": data.get("user_id"),
            "phone_number": phone_number,
            "answers": data.get("answers", []),
            "total_questions": data.get("total_questions", 10),
            "correct_answers": data.get("correct_answers", 0),
            "score_percentage": data.get("score_percentage", 0.0),
            "feedback": data.get("feedback", ""),
            "level": data.get("level", "beginner"),
            "completed_at": data.get("completed_at"),
            "time_spent_seconds": data.get("time_spent_seconds", 0),
            "created_at": datetime.datetime.now(tz=datetime.timezone.utc)
        }

        # Save assessment result
        assessment_collection.insert_one(assessment_result)

        return jsonify({"message": "Assessment result saved successfully"}), 200

    except jwt.ExpiredSignatureError:
        return jsonify({"error": "Token has expired"}), 401
    except jwt.InvalidTokenError:
        return jsonify({"error": "Invalid token"}), 401
    except Exception as e:
        return jsonify({"error": f"Failed to save assessment result: {str(e)}"}), 500

@app.route('/mark-assessment-completed', methods=['POST'])
def mark_assessment_completed():
    token = request.headers.get('Authorization')
    if not token or not token.startswith('Bearer '):
        return jsonify({"error": "Authorization token required"}), 401

    token = token.split(' ')[1]

    try:
        decoded_token = jwt.decode(token, ACCESS_TOKEN_SECRET, algorithms=['HS256'])
        phone_number = decoded_token['phone_number']

        # Update user profile to mark assessment as completed
        result = user_collection.update_one(
            {"phone_number": phone_number, "access_token": token},
            {"$set": {"assessment_completed": True}}
        )

        if result.matched_count == 0:
            return jsonify({"error": "User not found"}), 404

        return jsonify({"message": "Assessment marked as completed"}), 200

    except jwt.ExpiredSignatureError:
        return jsonify({"error": "Token has expired"}), 401
    except jwt.InvalidTokenError:
        return jsonify({"error": "Invalid token"}), 401
    except Exception as e:
        return jsonify({"error": f"Failed to mark assessment completed: {str(e)}"}), 500

if __name__ == '__main__':
    app.run(host="0.0.0.0", port=8001, debug=True)
