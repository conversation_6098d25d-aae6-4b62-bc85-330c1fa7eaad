// import 'package:flutter/material.dart';
// import 'package:fluid_bottom_nav_bar/fluid_bottom_nav_bar.dart';
// import 'package:provider/provider.dart';
// import 'providers/trial_status_provider.dart';

// class Footer extends StatefulWidget {
//   final Function(int) onTabSelected;

//   const Footer({
//     super.key,
//     required this.onTabSelected,
//   });

//   @override
//   State<Footer> createState() => _FooterState();
// }

// class _FooterState extends State<Footer> {
//   // This new method handles the logic for tab selection
//   void _handleTabChange(int index) {
//     // Get the provider instance. 'listen: false' is used because we are calling a method,
//     // not rebuilding the UI based on a data change here.
//     final trialProvider = Provider.of<TrialStatusProvider>(context, listen: false);

//     // Map the index to a feature name
//     String featureName;
//     String featureDisplayName; // A user-friendly name for the dialog

//     switch (index) {
//       case 0:
//         featureName = 'home';
//         featureDisplayName = 'Home';
//         break;
//       case 1:
//         featureName = 'explore';
//         featureDisplayName = 'Explore';
//         break;
//       case 2:
//         featureName = 'chat';
//         featureDisplayName = 'Scenario';
//         break;
//       case 3:
//         featureName = 'role_module';
//         featureDisplayName = 'Role Module';
//         break;
//       default:
//         // If the index is unknown, do nothing.
//         return;
//     }

//     // Check if the feature is accessible
//     if (trialProvider.isFeatureAccessible(featureName)) {
//       // If accessible, call the original onTabSelected function to navigate
//       widget.onTabSelected(index);
//     } else {
//       // If not accessible, show the upgrade dialog and do NOT navigate
//       trialProvider.showUpgradeDialog(context, featureName: featureDisplayName);
//     }
//   }

//   Color _getDynamicBackgroundColor(BuildContext context) {
//     return Theme.of(context).brightness == Brightness.light
//         ? Colors.white
//         : const Color.fromARGB(255, 6, 6, 6);
//   }

//   Color _getDynamicIconColor(BuildContext context) {
//     return Theme.of(context).brightness == Brightness.light
//         ? Colors.white
//         : const Color.fromARGB(255, 237, 240, 239);
//   }

//   @override
//   Widget build(BuildContext context) {
//     final isDarkMode = Theme.of(context).brightness == Brightness.dark;
//     return Stack(
//       alignment: Alignment.bottomCenter,
//       children: [
//         // Neon glow effect at the bottom
//         Positioned(
//           bottom: 0,
//           left: 0,
//           right: 0,
//           height: 4,
//           child: Container(
//             color: Colors.teal,
//             child: Container(
//               decoration: BoxDecoration(
//                 boxShadow: [
//                   BoxShadow(
//                     color: Colors.teal.withOpacity(isDarkMode ? 0.6 : 0.4),
//                     blurRadius: 12,
//                     spreadRadius: 2,
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         ),
//         // Main navigation bar
//         Consumer<TrialStatusProvider>(
//           builder: (context, trialProvider, child) {
//             final isChatLocked = !trialProvider.isFeatureAccessible('chat');
//             final isRoleLocked = !trialProvider.isFeatureAccessible('role_module');

//             return FluidNavBar(
//               icons: [
//                 FluidNavBarIcon(
//                   icon: Icons.home,
//                   backgroundColor: Colors.teal,
//                   extras: {"label": "Home"},
//                 ),
//                 FluidNavBarIcon(
//                   icon: Icons.explore,
//                   backgroundColor: Colors.teal,
//                   extras: {"label": "Explore"},
//                 ),
//                 FluidNavBarIcon(
//                   icon: isChatLocked ? Icons.lock : Icons.chat,
//                   backgroundColor: isChatLocked ? Colors.grey : Colors.teal,
//                   extras: {"label": isChatLocked ? "Locked" : "Scenario"},
//                 ),
//                 FluidNavBarIcon(
//                   icon: isRoleLocked ? Icons.lock : Icons.person,
//                   backgroundColor: isRoleLocked ? Colors.grey : Colors.teal,
//                   extras: {"label": isRoleLocked ? "Locked" : "Role Module"},
//                 ),
//               ],
//               // *** CHANGE IS HERE: Use our new handler function ***
//               onChange: _handleTabChange,
//               style: FluidNavBarStyle(
//                 iconUnselectedForegroundColor:
//                     _getDynamicIconColor(context).withOpacity(0.7),
//                 iconSelectedForegroundColor: _getDynamicIconColor(context),
//                 barBackgroundColor: _getDynamicBackgroundColor(context),
//               ),
//               scaleFactor: 5.0,
//               animationFactor: 2,
//             );
//           },
//         ),
//       ],
//     );
//   }
// }

import 'package:flutter/material.dart';
import 'package:fluid_bottom_nav_bar/fluid_bottom_nav_bar.dart';
import 'package:provider/provider.dart';
import 'providers/trial_status_provider.dart';

class Footer extends StatefulWidget {
  final Function(int) onTabSelected;

  const Footer({
    super.key,
    required this.onTabSelected,
  });

  @override
  State<Footer> createState() => _FooterState();
}

class _FooterState extends State<Footer> {
  // This method handles the logic for tab selection
  void _handleTabChange(int index) {
    // Get the provider instance
    final trialProvider = Provider.of<TrialStatusProvider>(context, listen: false);

    // Map the index to a feature name
    String featureName;
    String featureDisplayName;

    switch (index) {
      case 0:
        featureName = 'home';
        featureDisplayName = 'Home';
        break;
      case 1:
        featureName = 'explore';
        featureDisplayName = 'Explore';
        break;
      case 2:
        featureName = 'role_module';
        featureDisplayName = 'Role Module';
        break;
      default:
        // If the index is unknown, do nothing
        return;
    }

    // Check if the feature is accessible
    if (trialProvider.isFeatureAccessible(featureName)) {
      // If accessible, call the original onTabSelected function to navigate
      widget.onTabSelected(index);
    } else {
      // If not accessible, show the upgrade dialog and do NOT navigate
      trialProvider.showUpgradeDialog(context, featureName: featureDisplayName);
    }
  }

  Color _getDynamicBackgroundColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.light
        ? Colors.white
        : const Color.fromARGB(255, 6, 6, 6);
  }

  Color _getDynamicIconColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.light
        ? Colors.white
        : const Color.fromARGB(255, 237, 240, 239);
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        // Neon glow effect at the bottom
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          height: 4,
          child: Container(
            color: Colors.teal,
            child: Container(
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(
                    color: Colors.teal.withOpacity(isDarkMode ? 0.6 : 0.4),
                    blurRadius: 12,
                    spreadRadius: 2,
                  ),
                ],
              ),
            ),
          ),
        ),
        // Main navigation bar
        Consumer<TrialStatusProvider>(
          builder: (context, trialProvider, child) {
            final isRoleLocked = !trialProvider.isFeatureAccessible('role_module');

            return FluidNavBar(
              icons: [
                FluidNavBarIcon(
                  icon: Icons.home,
                  backgroundColor: Colors.teal,
                  extras: {"label": "Home"},
                ),
                FluidNavBarIcon(
                  icon: Icons.explore,
                  backgroundColor: Colors.teal,
                  extras: {"label": "Explore"},
                ),
                FluidNavBarIcon(
                  icon: isRoleLocked ? Icons.lock : Icons.person,
                  backgroundColor: isRoleLocked ? Colors.grey : Colors.teal,
                  extras: {"label": isRoleLocked ? "Locked" : "Role Module"},
                ),
              ],
              onChange: _handleTabChange,
              style: FluidNavBarStyle(
                iconUnselectedForegroundColor:
                    _getDynamicIconColor(context).withOpacity(0.7),
                iconSelectedForegroundColor: _getDynamicIconColor(context),
                barBackgroundColor: _getDynamicBackgroundColor(context),
              ),
              scaleFactor: 5.0,
              animationFactor: 2,
            );
          },
        ),
      ],
    );
  }
}