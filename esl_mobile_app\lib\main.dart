import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import 'package:e_sasthra/About/onboarding_1.dart';
import 'package:e_sasthra/Screens/chat_screen.dart';
import 'package:provider/provider.dart';
import 'package:responsive_framework/responsive_framework.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'firebase_options.dart';
import 'services/notification_service.dart';
import 'services/auth_service.dart';
import 'user_provider.dart';
import 'providers/trial_status_provider.dart';
import 'screens/home_screen.dart';
import 'screens/explore_screen.dart';
import 'screens/role_screen.dart';
import 'About/about1.dart';
import 'About/about2.dart';
import 'About/about4.dart';
import 'footer.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:animated_text_kit/animated_text_kit.dart';
import 'package:lottie/lottie.dart';

const String MOBILE = 'MOBILE';
const String TABLET = 'TABLET';
const String DESKTOP = 'DESKTOP';
const String FOUR_K = '4K';

// Background message handler for FCM
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  if (message.data.isNotEmpty) {
    await NotificationService.showNotification(
      title: message.data['title'] ?? 'No Title',
      body: message.data['body'] ?? 'No Body',
    );
  }
}

class ThemeProvider with ChangeNotifier {
  bool _isDarkMode = false; // Default to dark mode

  bool get isDarkMode => _isDarkMode;

  void toggleTheme(bool value) {
    _isDarkMode = value;
    notifyListeners();
  }

  ThemeData get themeData {
    if (_isDarkMode) {
      return ThemeData.dark().copyWith(
        primaryColor: const Color(0xFF1A1F35),
        scaffoldBackgroundColor: const Color(0xFF2D3250),
      );
    } else {
      return ThemeData.light().copyWith(
        primaryColor: const Color(0xFFFFFFFF),
        scaffoldBackgroundColor: const Color.fromARGB(255, 244, 246, 244),
      );
    }
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await dotenv.load(fileName: ".env");

  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  await NotificationService.initialize();
  await NotificationService.requestPermissions();

  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  final initialMessage = await FirebaseMessaging.instance.getInitialMessage();
  if (initialMessage != null) {
    print('Initial message: ${initialMessage.data}');
  }

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => UserProvider()),
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        ChangeNotifierProvider(create: (_) => TrialStatusProvider()),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return MaterialApp(
          debugShowCheckedModeBanner: false,
          title: 'My ESL App',
          theme: themeProvider.themeData,
          builder: (context, child) => ResponsiveBreakpoints.builder(
            child: child!,
            breakpoints: [
              const Breakpoint(start: 0, end: 450, name: MOBILE),
              const Breakpoint(start: 451, end: 800, name: TABLET),
              const Breakpoint(start: 801, end: 1000, name: DESKTOP),
              const Breakpoint(start: 1001, end: double.infinity, name: FOUR_K),
            ],
          ),
          home: const AuthCheck(),
        );
      },
    );
  }
}

class AuthCheck extends StatefulWidget {
  const AuthCheck({super.key});

  @override
  State<AuthCheck> createState() => _AuthCheckState();
}

class _AuthCheckState extends State<AuthCheck> {
  @override
  void initState() {
    super.initState();
    _checkAuthAndNavigate();
  }

  Future<void> _checkAuthAndNavigate() async {
    try {
      // Use the new AuthService to check authentication status
      final authStatus = await AuthService.checkAuthStatus();

      switch (authStatus) {
        case AuthStatus.authenticated:
        case AuthStatus.authenticatedTestUser:
          // Load user data and navigate to main page
          final userDataLoaded = await AuthService.loadUserData(context);
          if (userDataLoaded) {
            if (mounted) {
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(builder: (context) => const MainPage()),
              );
            }
          } else {
            // If user data loading fails, clear auth and go to onboarding
            await AuthService.clearAuthData();
            if (mounted) {
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(builder: (context) => const OnboardingScreen()),
              );
            }
          }
          break;

        case AuthStatus.notAuthenticated:
          // No valid authentication, go to onboarding
          if (mounted) {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(builder: (context) => const OnboardingScreen()),
            );
          }
          break;
      }
    } catch (e) {
      print('Error during authentication check: $e');
      // On error, clear auth data and go to onboarding
      await AuthService.clearAuthData();
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const OnboardingScreen()),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Lottie.asset(
          'assets/animations/loading_animation.json',
          width: 200,
          height: 200,
          fit: BoxFit.contain,
        ),
      ),
    );
  }
}

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          PageView(
            controller: _pageController,
            children: const [
              About1Page(),
              About2Page(),
              About4Page(),
              Onboarding1Page(),
            ],
          ),
          Positioned(
            bottom: 150,
            left: 0,
            right: 0,
            child: AnimatedBuilder(
              animation: _pageController,
              builder: (context, child) {
                double currentPage = _pageController.page ?? 0.0;
                return Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(5, (index) {
                    double activeness =
                        1 - (currentPage - index).abs().clamp(0.0, 1.0);
                    double width = 10 + 20 * activeness;
                    Color color = activeness > 0.5
                        ? const Color.fromARGB(255, 251, 249, 249)
                        : Color.fromARGB(255, 24, 183, 167);
                    return Container(
                      margin: const EdgeInsets.symmetric(horizontal: 5),
                      width: width,
                      height: 10,
                      decoration: BoxDecoration(
                        color: color,
                        borderRadius: BorderRadius.circular(5),
                      ),
                    );
                  }),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class MainPage extends StatefulWidget {
  const MainPage({super.key});

  @override
  State<MainPage> createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> {
  int _selectedIndex = 0;

  late List<Widget> _screens;

  @override
  void initState() {
    super.initState();
    _screens = [
      const HomeScreen(),
      const ExploreScreen(),
      const ChatScreen(),
      const RoleScreen(),
    ];

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final trialProvider = Provider.of<TrialStatusProvider>(context, listen: false);
      trialProvider.fetchTrialStatus();
    });
  }

  void _onItemTapped(int index) {
    final trialProvider = Provider.of<TrialStatusProvider>(context, listen: false);

    if (index == 2 && !trialProvider.isFeatureAccessible('chat')) {
      trialProvider.showUpgradeDialog(context, featureName: 'Chat/Scenario');
      return;
    }

    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color.fromARGB(255, 34, 136, 117),
      body: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        transitionBuilder: (Widget child, Animation<double> animation) {
          return FadeTransition(opacity: animation, child: child);
        },
        child: _screens[_selectedIndex],
        key: ValueKey<int>(_selectedIndex),
      ),
      bottomNavigationBar: Footer(onTabSelected: _onItemTapped),
    );
  }
}



