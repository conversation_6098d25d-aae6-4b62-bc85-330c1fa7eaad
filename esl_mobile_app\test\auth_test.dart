import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:e_sasthra/services/auth_service.dart';
import 'package:flutter/services.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('Authentication Service Tests', () {
    const storage = FlutterSecureStorage();

    setUp(() async {
      // Clear all stored data before each test
      await storage.deleteAll();
    });

    test('should return not authenticated when no tokens exist', () async {
      final authStatus = await AuthService.checkAuthStatus();
      expect(authStatus, AuthStatus.notAuthenticated);
    });

    test('should detect test user correctly', () async {
      // Store test user data
      await AuthService.storeAuthData(
        accessToken: 'test_access_token_123',
        refreshToken: 'test_refresh_token_123',
        phoneNumber: '+12025550123',
        isTestUser: true,
      );

      final authStatus = await AuthService.checkAuthStatus();
      expect(authStatus, AuthStatus.authenticatedTestUser);
    });

    test('should store and retrieve authentication data correctly', () async {
      const testPhone = '+1234567890';
      const testAccessToken = 'test_access_token';
      const testRefreshToken = 'test_refresh_token';

      await AuthService.storeAuthData(
        accessToken: testAccessToken,
        refreshToken: testRefreshToken,
        phoneNumber: testPhone,
        isTestUser: false,
      );

      // Verify data is stored correctly
      final storedAccessToken = await storage.read(key: 'access_token');
      final storedRefreshToken = await storage.read(key: 'refresh_token');
      final storedPhoneNumber = await storage.read(key: 'phone_number');
      final storedIsTestUser = await storage.read(key: 'is_test_user');

      expect(storedAccessToken, testAccessToken);
      expect(storedRefreshToken, testRefreshToken);
      expect(storedPhoneNumber, testPhone);
      expect(storedIsTestUser, 'false');
    });

    test('should clear authentication data correctly', () async {
      // First store some data
      await AuthService.storeAuthData(
        accessToken: 'test_token',
        refreshToken: 'test_refresh',
        phoneNumber: '+1234567890',
        isTestUser: false,
      );

      // Clear the data
      await AuthService.clearAuthData();

      // Verify all data is cleared
      final accessToken = await storage.read(key: 'access_token');
      final refreshToken = await storage.read(key: 'refresh_token');
      final phoneNumber = await storage.read(key: 'phone_number');
      final isTestUser = await storage.read(key: 'is_test_user');

      expect(accessToken, isNull);
      expect(refreshToken, isNull);
      expect(phoneNumber, isNull);
      expect(isTestUser, isNull);
    });

    test('should normalize phone numbers correctly', () async {
      // Store phone number with spaces
      await AuthService.storeAuthData(
        accessToken: 'test_token',
        refreshToken: 'test_refresh',
        phoneNumber: '****** 555 0123',
        isTestUser: false,
      );

      // Verify phone number is stored without spaces
      final storedPhoneNumber = await storage.read(key: 'phone_number');
      expect(storedPhoneNumber, '+12025550123');
    });

    test('should identify test user correctly', () async {
      // Store test user phone number
      await AuthService.storeAuthData(
        accessToken: 'test_token',
        refreshToken: 'test_refresh',
        phoneNumber: '+12025550123',
        isTestUser: true,
      );

      final isTestUser = await AuthService.isTestUser();
      expect(isTestUser, isTrue);
    });

    test('should get test user data correctly', () async {
      final testUserData = AuthService.getTestUserData();
      
      expect(testUserData['userId'], 'test_user_123');
      expect(testUserData['firstName'], 'Test');
      expect(testUserData['lastName'], 'User');
      expect(testUserData['defaultOtp'], '123456');
    });
  });
}
