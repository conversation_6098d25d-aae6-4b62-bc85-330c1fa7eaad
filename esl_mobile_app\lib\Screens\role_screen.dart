import 'dart:math';

import 'package:flutter/material.dart';
import 'package:e_sasthra/user_provider.dart' as user_provider;
import 'package:provider/provider.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'role_play_chat_screen.dart';
import '../widgets/star_loading_animation.dart';

final Map<String, List<String>> themesAndRoles = {
  'Interview': ['Interviewer', 'Candidate'],
  'Restaurant': ['Waiter', 'Customer'],
  'Movie Theatre': ['Ticket Clerk', 'Customer'],
  'Beach': ['Lifeguard', 'Tourist'],
  'Doctor Visit': ['Doctor', 'Patient'],
  'Shopping': ['Shopkeeper', 'Customer'],
  'Hotel': ['Receptionist', 'Guest'],
  'Airport': ['Immigration Officer', 'Traveler'],
};

final Map<String, String> themeImages = {
  'Interview':
      'https://pilbox.themuse.com/image.png?filter=antialias&h=350&opt=1&pos=top-left&prog=1&q=keep&url=https%3A%2F%2Fcms-assets.themuse.com%2Fmedia%2Flead%2Fcommon-interview-questions.png&w=700',
  'Restaurant':
      'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
  'Movie Theatre':
      'https://images.unsplash.com/photo-1489599849927-2ee91cede3ba?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
  'Beach':
      'https://images.unsplash.com/photo-1507525428034-b723cf961d3e?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
  'Doctor Visit':
      'https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
  'Shopping':
      'https://images.unsplash.com/photo-1441984904996-e0b6ba687e04?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
  'Hotel':
      'https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
  'Airport':
      'https://media.istockphoto.com/id/**********/photo/empty-airport-terminal-lounge-with-airplane-on-background.jpg?s=2048x2048&w=is&k=20&c=Ur5eJvj8txObUVmxFvb6tzFsjTHCheZdKcpYyDquck8=',
};

class RoleScreen extends StatefulWidget {
  const RoleScreen({super.key});

  @override
  State<RoleScreen> createState() => _RoleScreenState();
}

class _RoleScreenState extends State<RoleScreen>
    with SingleTickerProviderStateMixin {
  String? _selectedTheme;
  String? _selectedUserRole;
  String? _selectedBotRole;
  bool _isLoading = false;
  late AnimationController _flipController;
  final Map<String, bool> _cardFlipStates = {};


  @override
  void initState() {
    super.initState();
    _flipController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    // Initialize flip states
    for (var theme in themesAndRoles.keys) {
      _cardFlipStates[theme] = false;
    }
  }

  @override
  void dispose() {
    _flipController.dispose();
    super.dispose();
  }

  void _selectTheme(String theme) {
    setState(() {
      _selectedTheme = theme;
      _selectedUserRole = null;
      _selectedBotRole = null;
    });
  }

  void _toggleCardFlip(String theme) {
    setState(() {
      _cardFlipStates[theme] = !_cardFlipStates[theme]!;
    });

    if (_cardFlipStates[theme]!) {
      _flipController.forward();
    } else {
      _flipController.reverse();
    }
  }

  void _selectRole(String userRole, {String? theme}) {
    final currentTheme = theme ?? _selectedTheme;
    if (currentTheme == null) return;

    final rolesForTheme = themesAndRoles[currentTheme]!;
    final botRole = rolesForTheme.firstWhere((role) => role != userRole);

    setState(() {
      _selectedTheme = currentTheme;
      _selectedUserRole = userRole;
      _selectedBotRole = botRole;
    });

    _startSession();
  }

  Future<void> _startSession() async {
    if (_selectedTheme == null ||
        _selectedUserRole == null ||
        _selectedBotRole == null) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final userProvider =
          Provider.of<user_provider.UserProvider>(context, listen: false);
      final userId = userProvider.userId;
      final selectedTheme = _selectedTheme;
      final selectedUserRole = _selectedUserRole;
      final selectedBotRole = _selectedBotRole;

      final response = await http.post(
        Uri.parse('https://talktoai.in/rolestart'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'user_id': userId,
          'theme_topic': selectedTheme,
          'Role_of_Bot': selectedBotRole,
          'Role_of_user': selectedUserRole,
        }),
      );

      if (!mounted) return;

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final sessionId = data['session_id'];
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => RolePlayChatScreen(
              sessionId: sessionId,
              themeTopic: selectedTheme!,
              userRole: selectedUserRole!,
              botRole: selectedBotRole!,
              initialMessage: data['message'],
            ),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to start session: ${response.body}')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Widget _buildThemeCard(String theme, bool isFlipped) {
    return GestureDetector(
      onTap: () => _toggleCardFlip(theme),
      onLongPress: () => _selectTheme(theme),
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 500),
        transitionBuilder: (Widget child, Animation<double> animation) {
          final rotateAnim = Tween(begin: pi, end: 0.0).animate(animation);
          return AnimatedBuilder(
            animation: rotateAnim,
            child: child,
            builder: (context, widget) {
              return Transform(
                transform: Matrix4.rotationY(rotateAnim.value),
                alignment: Alignment.center,
                child: widget,
              );
            },
          );
        },
        child: isFlipped ? _buildCardBack(theme) : _buildCardFront(theme),
      ),
    );
  }

  Widget _buildCardFront(String theme) {
    return Container(
      key: ValueKey('front_$theme'),
      constraints: const BoxConstraints(
        minHeight: 180,
        maxHeight: 220,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withOpacity(0.2),
            blurRadius: 8,
            spreadRadius: 2,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Stack(
          fit: StackFit.expand,
          children: [
            Image.network(
              themeImages[theme]!,
              fit: BoxFit.cover,
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return Container(
                  color: Theme.of(context).colorScheme.surfaceVariant,
                  child: Center(
                    child: StarLoadingAnimation(size: 50),
                  ),
                );
              },
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: Theme.of(context).colorScheme.surfaceVariant,
                  child: Center(
                    child: Icon(
                      Icons.error_outline,
                      color: Theme.of(context).colorScheme.error,
                      size: 40,
                    ),
                  ),
                );
              },
            ),
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Theme.of(context).shadowColor.withOpacity(0.7),
                  ],
                ),
              ),
            ),
            Positioned(
              bottom: 16,
              left: 16,
              right: 16,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 4),
                  Text(
                    'Tap to see roles',
                    style: TextStyle(
                      fontSize: 14,
                      color: const Color.fromARGB(239, 217, 216, 216),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCardBack(String theme) {
    final roles = themesAndRoles[theme]!;

    return Container(
      key: ValueKey('back_$theme'),
      constraints: const BoxConstraints(
        minHeight: 180,
        maxHeight: 220,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: Theme.of(context).colorScheme.surfaceVariant,
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withOpacity(0.2),
            blurRadius: 8,
            spreadRadius: 2,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              theme,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ...roles.map((role) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      minimumSize: const Size(double.infinity, 48),
                    ),
                    onPressed: () => _selectRole(role, theme: theme),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.person_outline, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          textAlign: TextAlign.center,
                          role,
                          style: TextStyle(
                            fontSize: 16,
                            color: Theme.of(context).colorScheme.onPrimary,
                          ),
                        ),
                      ],
                    ),
                  ),
                )),
            const SizedBox(height: 8),
            TextButton(
              onPressed: () => _toggleCardFlip(theme),
              child: Text(
                'Back',
                style: TextStyle(
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThemeSelection() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 16, bottom: 8),
          child: Text(
            'Select Role-play Theme',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onBackground,
                ),
            textAlign: TextAlign.center,
          ),
        ),
        Expanded(
          child: GridView.count(
            padding: const EdgeInsets.all(16),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 0.8,
            children: themesAndRoles.keys.map((theme) {
              return _buildThemeCard(theme, _cardFlipStates[theme]!);
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildRoleSelection() {
    if (_selectedTheme == null) {
      return const Center(child: Text("Error: No theme selected."));
    }
    final roles = themesAndRoles[_selectedTheme!]!;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Text(
                    'Theme: $_selectedTheme',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Choose Your Role',
                    style: Theme.of(context).textTheme.titleLarge,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),
          ...roles.map((role) {
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: ListTile(
                  leading: Icon(Icons.person_outline,
                      color: Theme.of(context).colorScheme.primary),
                  title: Text(role),
                  trailing: Icon(Icons.arrow_forward,
                      color: Theme.of(context).colorScheme.primary),
                  onTap: _isLoading ? null : () => _selectRole(role),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            );
          }),
          const SizedBox(height: 20),
          if (_isLoading)
            const Center(
              child: StarLoadingAnimation(size: 80),
            )
          else
            ElevatedButton.icon(
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primaryContainer,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.symmetric(vertical: 14),
              ),
              icon: Icon(Icons.arrow_back,
                  color: Theme.of(context).colorScheme.onPrimaryContainer),
              label: Text(
                'Choose a different theme',
                style: TextStyle(
                    color: Theme.of(context).colorScheme.onPrimaryContainer),
              ),
              onPressed: () {
                setState(() {
                  _selectedTheme = null;
                  _selectedUserRole = null;
                  _selectedBotRole = null;
                });
              },
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
            _selectedTheme == null ? 'Role-play Practice' : 'Select Your Role'),
        centerTitle: true,
        elevation: 0,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(16),
          ),
        ),
      ),
      body: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        child: _selectedTheme == null
            ? _buildThemeSelection()
            : _buildRoleSelection(),
      ),
    );
  }
}
