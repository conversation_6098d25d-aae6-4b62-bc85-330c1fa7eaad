import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_svg/flutter_svg.dart';

class DailyVocabularyCard extends StatefulWidget {
  const DailyVocabularyCard({super.key});

  @override
  State<DailyVocabularyCard> createState() => _DailyVocabularyCardState();
}

class _DailyVocabularyCardState extends State<DailyVocabularyCard> {
  String _word = '';
  String _meaning = '';
  String _story = '';
  bool _isLoading = true;
  late FlutterTts flutterTts;

  @override
  void initState() {
    super.initState();
    flutterTts = FlutterTts();
    loadOrFetchVocabulary();
  }

  @override
  void dispose() {
    flutterTts.stop();
    super.dispose();
  }

  Future<void> _speakPronunciation() async {
    if (_word.isNotEmpty) {
      await flutterTts.setLanguage("en-US");
      await flutterTts.setPitch(1.0);
      await flutterTts.setSpeechRate(0.45);
      await flutterTts.speak(_word);
    }
  }

  Future<void> loadOrFetchVocabulary() async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now().toIso8601String().split('T').first;

    final storedDate = prefs.getString('vocab_date');
    final storedWord = prefs.getString('vocab_word');
    final storedPronunciation = prefs.getString('vocab_pronunciation');
    final storedMeaning = prefs.getString('vocab_meaning');
    final storedStory = prefs.getString('vocab_story');

    if (storedDate == today &&
        storedWord != null &&
        storedPronunciation != null &&
        storedMeaning != null &&
        storedStory != null) {
      setState(() {
        _word = storedWord;
        _meaning = storedMeaning;
        _story = storedStory;
        _isLoading = false;
      });
    } else {
      await fetchVocabulary(prefs, today);
    }
  }

  Future<void> fetchVocabulary(SharedPreferences prefs, String today) async {
    final apiKey = dotenv.env['OPENAI_API_KEY'];

    if (apiKey == null || apiKey.isEmpty) {
      setState(() {
        _word = '';
        _meaning = '';
        _story = "OpenAI API key not found. Add it to .env file.";
        _isLoading = false;
      });
      return;
    }

    const prompt = '''
Give me one advanced English vocabulary word with its meaning, IPA pronunciation, and a short funny story (maximum 50 words) to remember the word.

Format:
Word: <word>
Pronunciation: <IPA pronunciation>
Meaning: <meaning>
Story: <funny story in under 50 words>
''';

    try {
      final response = await http.post(
        Uri.parse("https://api.openai.com/v1/chat/completions"),
        headers: {
          'Authorization': 'Bearer $apiKey',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          "model": "gpt-4o-mini-2024-07-18",
          "messages": [
            {"role": "user", "content": prompt}
          ],
          "max_tokens": 200,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final content = data['choices'][0]['message']['content'];
        _parseAndStoreVocabulary(content, prefs, today);
      } else {
        setState(() {
          _story = "Error ${response.statusCode}: Failed to load vocabulary.";
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _story = "Exception occurred: $e";
        _isLoading = false;
      });
    }
  }

  Widget _buildBackgroundImage() {
    return SvgPicture.asset(
      'assets/Vocabulary_card_bg.svg',
      fit: BoxFit.cover,
      width: double.infinity,
      height: double.infinity,
    );
  }

  void _parseAndStoreVocabulary(
      String content, SharedPreferences prefs, String today) {
    final lines = content.split('\n');
    String word = '', pronunciation = '', meaning = '', story = '';

    for (var line in lines) {
      if (line.toLowerCase().startsWith('word:')) {
        word = line.substring(5).trim();
      } else if (line.toLowerCase().startsWith('pronunciation:')) {
        pronunciation = line.substring(14).trim();
      } else if (line.toLowerCase().startsWith('meaning:')) {
        meaning = line.substring(8).trim();
      } else if (line.toLowerCase().startsWith('story:')) {
        story = line.substring(6).trim();
      }
    }

    prefs.setString('vocab_date', today);
    prefs.setString('vocab_word', word);
    prefs.setString('vocab_pronunciation', pronunciation);
    prefs.setString('vocab_meaning', meaning);
    prefs.setString('vocab_story', story);

    setState(() {
      _word = word;
      _meaning = meaning;
      _story = story;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      margin: const EdgeInsets.all(16),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.blue.shade50,
                Colors.purple.shade50,
                Colors.teal.shade50,
              ],
            ),
          ),
          child: Stack(
            children: [
              Positioned.fill(child: _buildBackgroundImage()),
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Word:',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontFamily: 'Poppins',
                              fontSize: 16,
                              color: isDarkMode ? Colors.black : null,
                            ),
                          ),
                          Text(
                            _word,
                            style: TextStyle(
                              color: isDarkMode ? Colors.black : Colors.teal,
                              fontSize: 20,
                              fontFamily: 'Poppins',
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              IconButton(
                                icon: const Icon(Icons.volume_up),
                                color: Colors.blueAccent,
                                onPressed: _speakPronunciation,
                                tooltip: 'Hear it!',
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Text(
                            'Meaning:',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontFamily: 'Poppins',
                              fontSize: 16,
                              color: isDarkMode ? Colors.black : null,
                            ),
                          ),
                          Text(
                            _meaning,
                            style: TextStyle(
                              color:
                                  isDarkMode ? Colors.black : Colors.deepPurple,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Story:',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontFamily: 'Poppins',
                              fontSize: 16,
                              color: isDarkMode ? Colors.black : null,
                            ),
                          ),
                          Text(
                            _story,
                            style: TextStyle(
                              fontSize: 15,
                              color: isDarkMode ? Colors.black : null,
                            ),
                          ),
                        ],
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
