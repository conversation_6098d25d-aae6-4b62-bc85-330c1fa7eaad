import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:http/http.dart' as http;
import 'package:e_sasthra/Screens/plans_data.dart';
import 'package:razorpay_flutter/razorpay_flutter.dart';
import 'package:provider/provider.dart';
import 'package:lottie/lottie.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'dart:ui';
import '../user_provider.dart';
import 'privacy_policy_screen.dart';

// --- Color Constants ---
const Color textColor = Color.fromARGB(255, 228, 230, 232); // Dark Blue
const Color buttonColor = Color(0xFF388E3C); // Green
const Color priceColor = Color(0xFFD32F2F); // Red Accent
const Color oldPriceColor = Colors.grey;
const Color successColor = Color(0xFF2E7D32); // Darker Green for Success
const Color errorColor = Color(0xFFC62828); // Darker Red for Error
const Color warningColor = Colors.orange; // For the offer banner
const Color platecolor = Color(0xFF265073);

// --- Custom PlanCard Widget (with Glass Effect) ---
class PlanCard extends StatefulWidget {
  final Map<String, dynamic> plan;
  final Function(Map<String, dynamic>) onTap;
  final String currency;

  const PlanCard({
    super.key,
    required this.plan,
    required this.onTap,
    required this.currency,
  });

  @override
  _PlanCardState createState() => _PlanCardState();
}

class _PlanCardState extends State<PlanCard> {
  bool isTapped = false;

  @override
  Widget build(BuildContext context) {
    // Determine price and old price based on currency
    final String displayPrice = widget.currency == "INR"
        ? "₹ ${widget.plan["price"]}"
        : "\$ ${widget.plan["price-USD"]}";
    final String displayOldPrice = widget.currency == "INR"
        ? "₹ ${widget.plan["oldPrice"]}"
        : "\$ ${widget.plan["oldPrice-USD"]}";

    return GestureDetector(
      onTapDown: (_) => setState(() => isTapped = true),
      onTapUp: (_) {
        setState(() => isTapped = false);
        widget.onTap(widget.plan); // Trigger the payment process
      },
      onTapCancel: () => setState(() => isTapped = false),
      child: Transform.scale(
        scale: isTapped ? 0.95 : 1.0, // Scale effect on tap
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20), // Slightly more rounded
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8), // Adjusted blur
            child: Container(
              width: MediaQuery.of(context).size.width * 0.8,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.25), // Glass effect
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.white.withOpacity(0.4), width: 1),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.15),
                    offset: const Offset(0, 6),
                    blurRadius: 12,
                  ),
                ],
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    widget.plan["name"],
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      color: Color.fromARGB(255, 240, 240, 240),
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    widget.plan["price"] == 0 ? "Free" : displayPrice,
                    style: const TextStyle(
                      fontSize: 26,
                      fontWeight: FontWeight.bold,
                      color: priceColor,
                    ),
                  ),
                  const SizedBox(height: 6),
                  if (widget.plan["price"] != 0)
                    Text(
                      displayOldPrice,
                      style: const TextStyle(
                        decoration: TextDecoration.lineThrough,
                        color: oldPriceColor,
                        fontSize: 16,
                      ),
                    ),
                  const SizedBox(height: 15),
                  Expanded(
                    child: ListView.builder(
                      padding: EdgeInsets.zero,
                      shrinkWrap: true,
                      itemCount: (widget.plan["detail"] as List).length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4.0),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Icon(Icons.check_circle, color: Colors.green[800], size: 18),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  widget.plan["detail"][index],
                                  style: const TextStyle(fontSize: 14, color: textColor),
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 15),
                  // Conditionally render the button for non-trial plans
                  if (widget.plan["price"] != 0)
                    ElevatedButton(
                      onPressed: () => widget.onTap(widget.plan),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: buttonColor,
                        padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 12),
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                        elevation: 5,
                      ),
                      child: const Text(
                        "Buy Now",
                        style: TextStyle(color: Colors.white, fontSize: 16),
                      ),
                    )
                  else
                    ElevatedButton(
                      onPressed: () => widget.onTap(widget.plan),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: buttonColor,
                        padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 12),
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                        elevation: 5,
                      ),
                      child: const Text(
                        "Activate Trial",
                        style: TextStyle(color: Colors.white, fontSize: 16),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// --- Main ProScreen Widget ---
class ProScreen extends StatefulWidget {
  const ProScreen({super.key});

  @override
  _PaymentPageState createState() => _PaymentPageState();
}

class _PaymentPageState extends State<ProScreen> with TickerProviderStateMixin {
  bool popupVisible = false;
  String currency = "INR";
  Map<String, dynamic>? selectedPlan;
  List<dynamic> plans = [];
  late Razorpay _razorpay;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  late AnimationController _backgroundController;
  Alignment _gradientBegin = Alignment.topLeft;
  Alignment _gradientEnd = Alignment.bottomRight;
  bool _isLoading = true;
  final FlutterSecureStorage _storage = const FlutterSecureStorage();
  bool _showTrialNotification = false;
  DateTime? _trialExpiryDate;

  static const String _baseUrl = 'https://talktoai.in';

  @override
  void initState() {
    super.initState();
    _initializeScreen();

    _fadeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _backgroundController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 6),
    )..repeat(reverse: true);

    _backgroundController.addListener(() {
      setState(() {
        _gradientBegin = Alignment(
          _backgroundController.value * 2 - 1,
          _backgroundController.value * -2 + 1,
        );
        _gradientEnd = Alignment(
          _backgroundController.value * -2 + 1,
          _backgroundController.value * 2 - 1,
        );
      });
    });

    _razorpay = Razorpay();
    _razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
    _razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
    _razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);
  }

  Future<void> _initializeScreen() async {
    setState(() => _isLoading = true);
    plans = PlanData.plans;
    await fetchLocation();
    await _checkTrialStatus();
    setState(() => _isLoading = false);
    _fadeController.forward();
  }

  @override
  void dispose() {
    _razorpay.clear();
    _fadeController.dispose();
    _backgroundController.dispose();
    super.dispose();
  }

  // --- Check Trial Plan Status ---
  Future<void> _checkTrialStatus() async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final userId = userProvider.userId;
    final accessToken = await _storage.read(key: 'access_token');

    if (userId == null || accessToken == null) return;

    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/payment-details'),
        headers: {'Authorization': 'Bearer $accessToken'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['plan_id'] == 'trial_plan' && data['status'] == 'active') {
          final expiryDate = DateTime.parse(data['expiry_date']);
          final now = DateTime.now();
          final daysLeft = expiryDate.difference(now).inDays;

          setState(() {
            _trialExpiryDate = expiryDate;
            _showTrialNotification = daysLeft <= 1 && daysLeft > 0;
          });

          if (_showTrialNotification) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Your trial plan expires on ${_trialExpiryDate!.toString().substring(0, 10)}. Upgrade now to continue enjoying premium features!',
                    style: const TextStyle(color: Colors.white),
                  ),
                  backgroundColor: warningColor,
                  duration: const Duration(seconds: 5),
                  action: SnackBarAction(
                    label: 'Upgrade',
                    textColor: Colors.white,
                    onPressed: () {
                      // Already on ProScreen, so no navigation needed
                    },
                  ),
                ),
              );
            });
          }
        }
      }
    } catch (e) {
      print('Error checking trial status: $e');
    }
  }

  Future<void> fetchLocation() async {
    try {
      final response = await http.get(Uri.parse("https://ipapi.co/json/")).timeout(const Duration(seconds: 5));
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final country = data["country_code"];
        setState(() {
          currency = (country == "IN") ? "INR" : "USD";
        });
      } else {
        setState(() {
          currency = "INR";
        });
      }
    } catch (e) {
      print("Error fetching location: $e");
      setState(() {
        currency = "INR";
      });
    }
  }

  // --- Show Trial Confirmation Popup ---
  void _showTrialConfirmationPopup(Map<String, dynamic> plan) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15.0)),
          title: Row(
            children: [
              Icon(Icons.info, color: platecolor, size: 28),
              const SizedBox(width: 10),
              Text("Activate Trial Plan", style: TextStyle(color: platecolor)),
            ],
          ),
          content: Text(
            "Would you like to activate the 7-day free trial for ${plan['name']}? You'll get full access to all premium features during this period.",
            style: TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              child: const Text("Cancel", style: TextStyle(fontSize: 16, color: Colors.grey)),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: const Text("Activate", style: TextStyle(fontSize: 16, color: successColor)),
              onPressed: () {
                Navigator.of(context).pop();
                _activateTrialPlan(plan);
              },
            ),
          ],
        );
      },
    );
  }

  // --- Activate Trial Plan ---
  Future<void> _activateTrialPlan(Map<String, dynamic> plan) async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final userId = userProvider.userId;

    if (userId == null) {
      _showErrorDialog("User not logged in. Please log in again.");
      return;
    }

    setState(() {
      selectedPlan = plan;
      _isLoading = true;
    });

    final accessToken = await _storage.read(key: 'access_token');
    if (accessToken == null) {
      setState(() {
        _isLoading = false;
        _showErrorDialog("Authentication token missing. Please log in again.");
      });
      return;
    }

    try {
      final paymentDetails = {
        'payment_id': null,
        'order_id': null,
        'signature': null,
        'plan_id': plan["razorpayProductId"] ?? "prod_TrialPlan",
        'user_id': userId,
        'currency': currency,
        'amount': 0,
      };

      final response = await http.post(
        Uri.parse('$_baseUrl/payment_success'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $accessToken',
        },
        body: json.encode(paymentDetails),
      ).timeout(const Duration(seconds: 15));

      setState(() => _isLoading = false);

      if (response.statusCode == 200) {
        _showSuccessDialog(
          "Trial Plan Activated!",
          "Your 7-day trial plan has been activated successfully.",
        );
        setState(() {
          _showTrialNotification = false;
        });
      } else {
        final errorBody = json.decode(response.body);
        _showErrorDialog(
          "Failed to Activate Trial",
          "Unable to activate trial plan: ${errorBody['error'] ?? response.reasonPhrase}",
        );
      }
    } catch (e) {
      setState(() => _isLoading = false);
      print("Trial Plan Activation Error: $e");
      _showErrorDialog(
        "Failed to Activate Trial",
        "Could not activate trial plan. Please try again or contact support.",
      );
    } finally {
      setState(() => selectedPlan = null);
    }
  }

  Future<void> handlePayment(Map<String, dynamic> plan) async {
    // Show confirmation popup for trial plan
    if  (plan["price"] == 0 || plan["razorpayProductId"] == "prod_TrialPlan") {
      _showTrialConfirmationPopup(plan);
      return;
    }

    // Handle paid plans
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final userId = userProvider.userId;
    final userName = userProvider.firstName ?? 'User';
    final userEmail = userProvider.email ?? '<EMAIL>';
    final userContact = userProvider.phoneNumber ?? '**********';

    if (userId == null) {
      _showErrorDialog("User not logged in. Please log in again.");
      return;
    }

    setState(() {
      selectedPlan = plan;
      _isLoading = true;
    });

    final accessToken = await _storage.read(key: 'access_token');
    if (accessToken == null) {
      setState(() {
        _isLoading = false;
        _showErrorDialog("Authentication token missing. Please log in again.");
      });
      return;
    }

    bool isInUSD = currency == "USD";
    String priceStr = (isInUSD ? plan["price-USD"] : plan["price"]).toString();
    priceStr = priceStr.replaceAll(RegExp(r'[^0-9.]'), '');
    double price = double.tryParse(priceStr) ?? 0.0;

    if (price <= 0) {
      setState(() => _isLoading = false);
      _showErrorDialog("Invalid plan price. Please contact support.");
      return;
    }

    int amountInSmallestUnit = (price * 100).round();

    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/create_order'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $accessToken',
        },
        body: json.encode({
          'amount': amountInSmallestUnit,
          'currency': currency,
          'user_id': userId,
          'plan_name': plan['name'],
        }),
      ).timeout(const Duration(seconds: 15));

      setState(() => _isLoading = false);

      if (response.statusCode == 200) {
        final orderData = json.decode(response.body);
        final orderId = orderData["order_id"];

        if (orderId == null || orderId.isEmpty) {
          throw Exception("Order ID not received from server.");
        }

        var options = {
          'key': 'rzp_live_slUPSvEfs6b1B9',
          'amount': amountInSmallestUnit.toString(),
          'currency': currency,
          'name': 'ESL Learning App',
          'description': 'Payment for ${plan["name"]}',
          'order_id': orderId,
          'prefill': {
            'name': userName,
            'email': userEmail,
            'contact': userContact,
          },
          'theme': {'color': '#3399cc'}
        };
        _razorpay.open(options);
      } else {
        final errorBody = json.decode(response.body);
        throw Exception("Failed to create order: ${errorBody['error'] ?? response.reasonPhrase}");
      }
    } catch (error) {
      setState(() => _isLoading = false);
      print("Order Creation Error: $error");
      _showErrorDialog(
        "Could not initiate payment",
        "Please check your connection and try again.",
      );
    }
  }

  void _handlePaymentSuccess(PaymentSuccessResponse response) async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final userId = userProvider.userId;

    if (userId == null || selectedPlan == null) {
      _showErrorDialog("An internal error occurred after payment. Please contact support.");
      return;
    }

    setState(() => _isLoading = true);

    bool isInUSD = currency == "USD";
    Map<String, dynamic> paymentDetails = {
      'payment_id': response.paymentId,
      'order_id': response.orderId,
      'signature': response.signature,
      'plan_name': selectedPlan!["name"],
      'plan_id': selectedPlan!["razorpayProductId"],
      'user_id': userId,
      'currency': currency,
      'amount': (double.tryParse((isInUSD
                  ? selectedPlan!["price-USD"]
                  : selectedPlan!["price"])
              .toString()
              .replaceAll(RegExp(r'[^0-9.]'), '')) ??
          0.0) *
          100.round()
    };

    try {
      final backendResponse = await http.post(
        Uri.parse('$_baseUrl/payment_success'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await _storage.read(key: 'access_token')}',
        },
        body: json.encode(paymentDetails),
      ).timeout(const Duration(seconds: 15));

      setState(() => _isLoading = false);

      if (backendResponse.statusCode == 200) {
        _showSuccessDialog(
          "Payment Successful!",
          "Thank you for subscribing to ${selectedPlan!['name']}. Your access has been activated.",
        );
        setState(() {
          _showTrialNotification = false;
        });
      } else {
        final errorBody = json.decode(backendResponse.body);
        throw Exception("Failed to verify payment on server: ${errorBody['error'] ?? backendResponse.reasonPhrase}");
      }
    } catch (e) {
      setState(() => _isLoading = false);
      print("Payment Verification Error: $e");
      _showSuccessDialog(
        "Payment Received!",
        "Your payment was successful, but we encountered an issue verifying it with our server. Please contact support if your access isn't updated shortly.",
      );
    } finally {
      setState(() {
        selectedPlan = null;
      });
    }
  }

  void _handlePaymentError(PaymentFailureResponse response) {
    print("Payment Failed: ${response.code} - ${response.message}");
    _showErrorDialog(
      "Payment Failed",
      "Your payment could not be processed. Please try again or use a different payment method.",
    );
    setState(() {
      _isLoading = false;
      selectedPlan = null;
    });
  }

  void _handleExternalWallet(ExternalWalletResponse response) {
    print("External Wallet Selected: ${response.walletName}");
    setState(() {
      _isLoading = false;
      selectedPlan = null;
    });
  }

  void _showSuccessDialog(String title, String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15.0)),
          title: Row(
            children: [
              Icon(Icons.check_circle, color: successColor, size: 28),
              const SizedBox(width: 10),
              Text(title, style: TextStyle(color: successColor)),
            ],
          ),
          content: ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.4,
              maxWidth: MediaQuery.of(context).size.width * 0.8,
            ),
            child: SingleChildScrollView(
              child: Text(message, style: TextStyle(fontSize: 16)),
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text("OK", style: TextStyle(fontSize: 16)),
              onPressed: () {
                Navigator.of(context).pop();
                Navigator.pushReplacementNamed(context, '/main');
              },
            ),
          ],
        );
      },
    );
  }

  void _showErrorDialog(String title, [String? message]) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15.0)),
          title: Row(
            children: [
              Icon(Icons.error, color: errorColor, size: 28),
              const SizedBox(width: 10),
              Text(title, style: TextStyle(color: errorColor)),
            ],
          ),
          content: ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.4,
              maxWidth: MediaQuery.of(context).size.width * 0.8,
            ),
            child: SingleChildScrollView(
              child: Text(
                message ?? "An unexpected error occurred. Please try again later.",
                style: TextStyle(fontSize: 16),
              ),
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text("OK", style: TextStyle(fontSize: 16)),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      appBar: AppBar(
        title: const Text("Popular Plans"),
        backgroundColor: platecolor.withOpacity(0.8),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: AnimatedBuilder(
        animation: _backgroundController,
        builder: (context, child) {
          return Container(
            width: double.infinity,
            height: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: const [
                  Color(0xFF265073),
                  Color(0xFF2D9596),
                  Color(0xFF9AD0C2),
                ],
                begin: _gradientBegin,
                end: _gradientEnd,
              ),
            ),
            child: child,
          );
        },
        child: SafeArea(
          child: Stack(
            children: [
              FadeTransition(
                opacity: _fadeAnimation,
                child: SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 20.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        const SizedBox(height: 20),
                        if (plans.isNotEmpty)
                          CarouselSlider(
                            options: CarouselOptions(
                              height: MediaQuery.of(context).size.height * 0.6,
                              enlargeCenterPage: true,
                              enableInfiniteScroll: false,
                              viewportFraction: 0.8,
                              aspectRatio: 16 / 9,
                              autoPlay: false,
                            ),
                            items: plans.map((plan) {
                              return PlanCard(
                                plan: plan,
                                onTap: handlePayment,
                                currency: currency,
                              );
                            }).toList(),
                          )
                        else if (!_isLoading)
                          const Center(
                            child: Padding(
                              padding: EdgeInsets.all(20.0),
                              child: Text(
                                "No plans available at the moment.",
                                style: TextStyle(color: Colors.white70, fontSize: 16),
                              ),
                            ),
                          ),
                        const SizedBox(height: 30),
                      ],
                    ),
                  ),
                ),
              ),
              Positioned(
                bottom: 20,
                right: 20,
                child: GestureDetector(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const PrivacyPolicyScreen(),
                      ),
                    );
                  },
                  child: Container(
                    padding: const EdgeInsets.all(8.0),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white.withOpacity(0.2),
                      border: Border.all(color: Colors.white.withOpacity(0.4)),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Lottie.asset(
                      'assets/animations/privacy_policy.json',
                      width: 60,
                      height: 150,
                      fit: BoxFit.contain,
                      repeat: true,
                    ),
                  ),
                ),
              ),
              if (_isLoading)
                Container(
                  color: Colors.black.withOpacity(0.5),
                  child: const Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}

// import 'dart:async';
// import 'dart:convert';
// import 'package:flutter/material.dart';
// import 'package:carousel_slider/carousel_slider.dart';
// import 'package:http/http.dart' as http;
// import 'package:e_sasthra/Screens/plans_data.dart';
// import 'package:razorpay_flutter/razorpay_flutter.dart';
// import 'package:provider/provider.dart';
// import 'package:lottie/lottie.dart';
// import 'package:flutter_secure_storage/flutter_secure_storage.dart';
// import 'dart:ui';
// import '../user_provider.dart';
// import 'privacy_policy_screen.dart';

// // --- Color Constants ---
// const Color textColor = Color.fromARGB(255, 228, 230, 232); // Dark Blue
// const Color buttonColor = Color(0xFF388E3C); // Green
// const Color priceColor = Color(0xFFD32F2F); // Red Accent
// const Color oldPriceColor = Colors.grey;
// const Color successColor = Color(0xFF2E7D32); // Darker Green for Success
// const Color errorColor = Color(0xFFC62828); // Darker Red for Error
// const Color warningColor = Colors.orange; // For the offer banner
// const Color platecolor = Color(0xFF265073);

// // --- Custom PlanCard Widget (with Glass Effect) ---
// class PlanCard extends StatefulWidget {
//   final Map<String, dynamic> plan;
//   final Function(Map<String, dynamic>) onTap;
//   final String currency;

//   const PlanCard({
//     super.key,
//     required this.plan,
//     required this.onTap,
//     required this.currency,
//   });

//   @override
//   _PlanCardState createState() => _PlanCardState();
// }

// class _PlanCardState extends State<PlanCard> {
//   bool isTapped = false;

//   @override
//   Widget build(BuildContext context) {
//     // Determine price and old price based on currency
//     final String displayPrice = widget.currency == "INR"
//         ? "₹ ${widget.plan["price"]}"
//         : "\$ ${widget.plan["price-USD"]}";
//     final String displayOldPrice = widget.currency == "INR"
//         ? "₹ ${widget.plan["oldPrice"]}"
//         : "\$ ${widget.plan["oldPrice-USD"]}";

//     return GestureDetector(
//       onTapDown: (_) => setState(() => isTapped = true),
//       onTapUp: (_) {
//         setState(() => isTapped = false);
//         widget.onTap(widget.plan); // Trigger the payment process
//       },
//       onTapCancel: () => setState(() => isTapped = false),
//       child: Transform.scale(
//         scale: isTapped ? 0.95 : 1.0, // Scale effect on tap
//         child: ClipRRect(
//           borderRadius: BorderRadius.circular(20), // Slightly more rounded
//           child: BackdropFilter(
//             filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8), // Adjusted blur
//             child: Container(
//               width: MediaQuery.of(context).size.width * 0.8,
//               decoration: BoxDecoration(
//                 color: Colors.white.withOpacity(0.25), // Glass effect
//                 borderRadius: BorderRadius.circular(20),
//                 border: Border.all(color: Colors.white.withOpacity(0.4), width: 1),
//                 boxShadow: [
//                   BoxShadow(
//                     color: Colors.black.withOpacity(0.15),
//                     offset: const Offset(0, 6),
//                     blurRadius: 12,
//                   ),
//                 ],
//               ),
//               padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 20.0),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.center,
//                 children: [
//                   Text(
//                     widget.plan["name"],
//                     textAlign: TextAlign.center,
//                     style: const TextStyle(
//                       fontSize: 22,
//                       fontWeight: FontWeight.bold,
//                       color: Color.fromARGB(255, 240, 240, 240),
//                     ),
//                   ),
//                   const SizedBox(height: 12),
//                   Text(
//                     widget.plan["price"] == 0 ? "Free" : displayPrice,
//                     style: const TextStyle(
//                       fontSize: 26,
//                       fontWeight: FontWeight.bold,
//                       color: priceColor,
//                     ),
//                   ),
//                   const SizedBox(height: 6),
//                   if (widget.plan["price"] != 0)
//                     Text(
//                       displayOldPrice,
//                       style: const TextStyle(
//                         decoration: TextDecoration.lineThrough,
//                         color: oldPriceColor,
//                         fontSize: 16,
//                       ),
//                     ),
//                   const SizedBox(height: 15),
//                   Expanded(
//                     child: ListView.builder(
//                       padding: EdgeInsets.zero,
//                       shrinkWrap: true,
//                       itemCount: (widget.plan["detail"] as List).length,
//                       itemBuilder: (context, index) {
//                         return Padding(
//                           padding: const EdgeInsets.symmetric(vertical: 4.0),
//                           child: Row(
//                             crossAxisAlignment: CrossAxisAlignment.start,
//                             children: [
//                               Icon(Icons.check_circle, color: Colors.green[800], size: 18),
//                               const SizedBox(width: 8),
//                               Expanded(
//                                 child: Text(
//                                   widget.plan["detail"][index],
//                                   style: const TextStyle(fontSize: 14, color: textColor),
//                                 ),
//                               ),
//                             ],
//                           ),
//                         );
//                       },
//                     ),
//                   ),
//                   const SizedBox(height: 15),
//                   // Conditionally render the button for trial plans only
//                   if (widget.plan["price"] == 0)
//                     ElevatedButton(
//                       onPressed: () => widget.onTap(widget.plan),
//                       style: ElevatedButton.styleFrom(
//                         backgroundColor: buttonColor,
//                         padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 12),
//                         shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
//                         elevation: 5,
//                       ),
//                       child: const Text(
//                         "Activate Trial",
//                         style: TextStyle(color: Colors.white, fontSize: 16),
//                       ),
//                     ),
//                 ],
//               ),
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }

// // --- Main ProScreen Widget ---
// class ProScreen extends StatefulWidget {
//   const ProScreen({super.key});

//   @override
//   _PaymentPageState createState() => _PaymentPageState();
// }

// class _PaymentPageState extends State<ProScreen> with TickerProviderStateMixin {
//   bool popupVisible = false;
//   String currency = "INR";
//   Map<String, dynamic>? selectedPlan;
//   List<dynamic> plans = [];
//   late Razorpay _razorpay;
//   late AnimationController _fadeController;
//   late Animation<double> _fadeAnimation;
//   late AnimationController _backgroundController;
//   Alignment _gradientBegin = Alignment.topLeft;
//   Alignment _gradientEnd = Alignment.bottomRight;
//   bool _isLoading = true;
//   final FlutterSecureStorage _storage = const FlutterSecureStorage();
//   bool _showTrialNotification = false;
//   DateTime? _trialExpiryDate;

//   static const String _baseUrl = 'https://talktoai.in';

//   @override
//   void initState() {
//     super.initState();
//     _initializeScreen();

//     _fadeController = AnimationController(
//       vsync: this,
//       duration: const Duration(milliseconds: 800),
//     );
//     _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
//       CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
//     );

//     _backgroundController = AnimationController(
//       vsync: this,
//       duration: const Duration(seconds: 6),
//     )..repeat(reverse: true);

//     _backgroundController.addListener(() {
//       setState(() {
//         _gradientBegin = Alignment(
//           _backgroundController.value * 2 - 1,
//           _backgroundController.value * -2 + 1,
//         );
//         _gradientEnd = Alignment(
//           _backgroundController.value * -2 + 1,
//           _backgroundController.value * 2 - 1,
//         );
//       });
//     });

//     _razorpay = Razorpay();
//     _razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
//     _razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
//     _razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);
//   }

//   Future<void> _initializeScreen() async {
//     setState(() => _isLoading = true);
//     plans = PlanData.plans;
//     await fetchLocation();
//     await _checkTrialStatus();
//     setState(() => _isLoading = false);
//     _fadeController.forward();
//   }

//   @override
//   void dispose() {
//     _razorpay.clear();
//     _fadeController.dispose();
//     _backgroundController.dispose();
//     super.dispose();
//   }

//   // --- Check Trial Plan Status ---
//   Future<void> _checkTrialStatus() async {
//     final userProvider = Provider.of<UserProvider>(context, listen: false);
//     final userId = userProvider.userId;
//     final accessToken = await _storage.read(key: 'access_token');

//     if (userId == null || accessToken == null) return;

//     try {
//       final response = await http.get(
//         Uri.parse('$_baseUrl/payment-details'),
//         headers: {'Authorization': 'Bearer $accessToken'},
//       );

//       if (response.statusCode == 200) {
//         final data = json.decode(response.body);
//         if (data['plan_id'] == 'trial_plan' && data['status'] == 'active') {
//           final expiryDate = DateTime.parse(data['expiry_date']);
//           final now = DateTime.now();
//           final daysLeft = expiryDate.difference(now).inDays;

//           setState(() {
//             _trialExpiryDate = expiryDate;
//             _showTrialNotification = daysLeft <= 1 && daysLeft > 0;
//           });

//           if (_showTrialNotification) {
//             WidgetsBinding.instance.addPostFrameCallback((_) {
//               ScaffoldMessenger.of(context).showSnackBar(
//                 SnackBar(
//                   content: Text(
//                     'Your trial plan expires on ${_trialExpiryDate!.toString().substring(0, 10)}. Upgrade now to continue enjoying premium features!',
//                     style: const TextStyle(color: Colors.white),
//                   ),
//                   backgroundColor: warningColor,
//                   duration: const Duration(seconds: 5),
//                   action: SnackBarAction(
//                     label: 'Upgrade',
//                     textColor: Colors.white,
//                     onPressed: () {
//                       // Already on ProScreen, so no navigation needed
//                     },
//                   ),
//                 ),
//               );
//             });
//           }
//         }
//       }
//     } catch (e) {
//       print('Error checking trial status: $e');
//     }
//   }

//   Future<void> fetchLocation() async {
//     try {
//       final response = await http.get(Uri.parse("https://ipapi.co/json/")).timeout(const Duration(seconds: 5));
//       if (response.statusCode == 200) {
//         final data = json.decode(response.body);
//         final country = data["country_code"];
//         setState(() {
//           currency = (country == "IN") ? "INR" : "USD";
//         });
//       } else {
//         setState(() {
//           currency = "INR";
//         });
//       }
//     } catch (e) {
//       print("Error fetching location: $e");
//       setState(() {
//         currency = "INR";
//       });
//     }
//   }

//   // --- Show Trial Confirmation Popup ---
//   void _showTrialConfirmationPopup(Map<String, dynamic> plan) {
//     showDialog(
//       context: context,
//       builder: (BuildContext context) {
//         return AlertDialog(
//           shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15.0)),
//           title: Row(
//             children: [
//               Icon(Icons.info, color: platecolor, size: 28),
//               const SizedBox(width: 10),
//               Text("Activate Trial Plan", style: TextStyle(color: platecolor)),
//             ],
//           ),
//           content: Text(
//             "Would you like to activate the 7-day free trial for ${plan['name']}? You'll get full access to all premium features during this period.",
//             style: TextStyle(fontSize: 16),
//           ),
//           actions: [
//             TextButton(
//               child: const Text("Cancel", style: TextStyle(fontSize: 16, color: Colors.grey)),
//               onPressed: () {
//                 Navigator.of(context).pop();
//               },
//             ),
//             TextButton(
//               child: const Text("Activate", style: TextStyle(fontSize: 16, color: successColor)),
//               onPressed: () {
//                 Navigator.of(context).pop();
//                 _activateTrialPlan(plan);
//               },
//             ),
//           ],
//         );
//       },
//     );
//   }

//   // --- Activate Trial Plan ---
//   Future<void> _activateTrialPlan(Map<String, dynamic> plan) async {
//     final userProvider = Provider.of<UserProvider>(context, listen: false);
//     final userId = userProvider.userId;

//     if (userId == null) {
//       _showErrorDialog("User not logged in. Please log in again.");
//       return;
//     }

//     setState(() {
//       selectedPlan = plan;
//       _isLoading = true;
//     });

//     final accessToken = await _storage.read(key: 'access_token');
//     if (accessToken == null) {
//       setState(() {
//         _isLoading = false;
//         _showErrorDialog("Authentication token missing. Please log in again.");
//       });
//       return;
//     }

//     try {
//       final paymentDetails = {
//         'payment_id': null,
//         'order_id': null,
//         'signature': null,
//         'plan_id': plan["razorpayProductId"] ?? "prod_TrialPlan",
//         'user_id': userId,
//         'currency': currency,
//         'amount': 0,
//       };

//       final response = await http.post(
//         Uri.parse('$_baseUrl/payment_success'),
//         headers: {
//           'Content-Type': 'application/json',
//           'Authorization': 'Bearer $accessToken',
//         },
//         body: json.encode(paymentDetails),
//       ).timeout(const Duration(seconds: 15));

//       setState(() => _isLoading = false);

//       if (response.statusCode == 200) {
//         _showSuccessDialog(
//           "Trial Plan Activated!",
//           "Your 7-day trial plan has been activated successfully.",
//         );
//         setState(() {
//           _showTrialNotification = false;
//         });
//       } else {
//         final errorBody = json.decode(response.body);
//         _showErrorDialog(
//           "Failed to Activate Trial",
//           "Unable to activate trial plan: ${errorBody['error'] ?? response.reasonPhrase}",
//         );
//       }
//     } catch (e) {
//       setState(() => _isLoading = false);
//       print("Trial Plan Activation Error: $e");
//       _showErrorDialog(
//         "Failed to Activate Trial",
//         "Could not activate trial plan. Please try again or contact support.",
//       );
//     } finally {
//       setState(() => selectedPlan = null);
//     }
//   }

//   Future<void> handlePayment(Map<String, dynamic> plan) async {
//     // Show confirmation popup for trial plan
//     if  (plan["price"] == 0 || plan["razorpayProductId"] == "prod_TrialPlan") {
//       _showTrialConfirmationPopup(plan);
//       return;
//     }

//     // Handle paid plans
//     final userProvider = Provider.of<UserProvider>(context, listen: false);
//     final userId = userProvider.userId;
//     final userName = userProvider.firstName ?? 'User';
//     final userEmail = userProvider.email ?? '<EMAIL>';
//     final userContact = userProvider.phoneNumber ?? '**********';

//     if (userId == null) {
//       _showErrorDialog("User not logged in. Please log in again.");
//       return;
//     }

//     setState(() {
//       selectedPlan = plan;
//       _isLoading = true;
//     });

//     final accessToken = await _storage.read(key: 'access_token');
//     if (accessToken == null) {
//       setState(() {
//         _isLoading = false;
//         _showErrorDialog("Authentication token missing. Please log in again.");
//       });
//       return;
//     }

//     bool isInUSD = currency == "USD";
//     String priceStr = (isInUSD ? plan["price-USD"] : plan["price"]).toString();
//     priceStr = priceStr.replaceAll(RegExp(r'[^0-9.]'), '');
//     double price = double.tryParse(priceStr) ?? 0.0;

//     if (price <= 0) {
//       setState(() => _isLoading = false);
//       _showErrorDialog("Invalid plan price. Please contact support.");
//       return;
//     }

//     int amountInSmallestUnit = (price * 100).round();

//     try {
//       final response = await http.post(
//         Uri.parse('$_baseUrl/create_order'),
//         headers: {
//           'Content-Type': 'application/json',
//           'Authorization': 'Bearer $accessToken',
//         },
//         body: json.encode({
//           'amount': amountInSmallestUnit,
//           'currency': currency,
//           'user_id': userId,
//           'plan_name': plan['name'],
//         }),
//       ).timeout(const Duration(seconds: 15));

//       setState(() => _isLoading = false);

//       if (response.statusCode == 200) {
//         final orderData = json.decode(response.body);
//         final orderId = orderData["order_id"];

//         if (orderId == null || orderId.isEmpty) {
//           throw Exception("Order ID not received from server.");
//         }

//         var options = {
//           'key': 'rzp_live_slUPSvEfs6b1B9',
//           'amount': amountInSmallestUnit.toString(),
//           'currency': currency,
//           'name': 'ESL Learning App',
//           'description': 'Payment for ${plan["name"]}',
//           'order_id': orderId,
//           'prefill': {
//             'name': userName,
//             'email': userEmail,
//             'contact': userContact,
//           },
//           'theme': {'color': '#3399cc'}
//         };
//         _razorpay.open(options);
//       } else {
//         final errorBody = json.decode(response.body);
//         throw Exception("Failed to create order: ${errorBody['error'] ?? response.reasonPhrase}");
//       }
//     } catch (error) {
//       setState(() => _isLoading = false);
//       print("Order Creation Error: $error");
//       _showErrorDialog(
//         "Could not initiate payment",
//         "Please check your connection and try again.",
//       );
//     }
//   }

//   void _handlePaymentSuccess(PaymentSuccessResponse response) async {
//     final userProvider = Provider.of<UserProvider>(context, listen: false);
//     final userId = userProvider.userId;

//     if (userId == null || selectedPlan == null) {
//       _showErrorDialog("An internal error occurred after payment. Please contact support.");
//       return;
//     }

//     setState(() => _isLoading = true);

//     bool isInUSD = currency == "USD";
//     Map<String, dynamic> paymentDetails = {
//       'payment_id': response.paymentId,
//       'order_id': response.orderId,
//       'signature': response.signature,
//       'plan_name': selectedPlan!["name"],
//       'plan_id': selectedPlan!["razorpayProductId"],
//       'user_id': userId,
//       'currency': currency,
//       'amount': (double.tryParse((isInUSD
//                   ? selectedPlan!["price-USD"]
//                   : selectedPlan!["price"])
//               .toString()
//               .replaceAll(RegExp(r'[^0-9.]'), '')) ??
//           0.0) *
//           100.round()
//     };

//     try {
//       final backendResponse = await http.post(
//         Uri.parse('$_baseUrl/payment_success'),
//         headers: {
//           'Content-Type': 'application/json',
//           'Authorization': 'Bearer ${await _storage.read(key: 'access_token')}',
//         },
//         body: json.encode(paymentDetails),
//       ).timeout(const Duration(seconds: 15));

//       setState(() => _isLoading = false);

//       if (backendResponse.statusCode == 200) {
//         _showSuccessDialog(
//           "Payment Successful!",
//           "Thank you for subscribing to ${selectedPlan!['name']}. Your access has been activated.",
//         );
//         setState(() {
//           _showTrialNotification = false;
//         });
//       } else {
//         final errorBody = json.decode(backendResponse.body);
//         throw Exception("Failed to verify payment on server: ${errorBody['error'] ?? backendResponse.reasonPhrase}");
//       }
//     } catch (e) {
//       setState(() => _isLoading = false);
//       print("Payment Verification Error: $e");
//       _showSuccessDialog(
//         "Payment Received!",
//         "They're still working on processing your payment, so your access hasn't been updated yet. If it takes too long, please reach out to support for help.",
//       );
//     } finally {
//       setState(() {
//         selectedPlan = null;
//       });
//     }
//   }

//   void _handlePaymentError(PaymentFailureResponse response) {
//     print("Payment Failed: ${response.code} - ${response.message}");
//     _showErrorDialog(
//       "Payment Failed",
//       "Your payment could not be processed. Please try again or use a different payment method.",
//     );
//     setState(() {
//       _isLoading = false;
//       selectedPlan = null;
//     });
//   }

//   void _handleExternalWallet(ExternalWalletResponse response) {
//     print("External Wallet Selected: ${response.walletName}");
//     setState(() {
//       _isLoading = false;
//       selectedPlan = null;
//     });
//   }

//   void _showSuccessDialog(String title, String message) {
//     showDialog(
//       context: context,
//       barrierDismissible: false,
//       builder: (BuildContext context) {
//         return AlertDialog(
//           shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15.0)),
//           title: Row(
//             children: [
//               Icon(Icons.check_circle, color: successColor, size: 28),
//               const SizedBox(width: 10),
//               Text(title, style: TextStyle(color: successColor)),
//             ],
//           ),
//           content: ConstrainedBox(
//             constraints: BoxConstraints(
//               maxHeight: MediaQuery.of(context).size.height * 0.4,
//               maxWidth: MediaQuery.of(context).size.width * 0.8,
//             ),
//             child: SingleChildScrollView(
//               child: Text(message, style: TextStyle(fontSize: 16)),
//             ),
//           ),
//           actions: <Widget>[
//             TextButton(
//               child: const Text("OK", style: TextStyle(fontSize: 16)),
//               onPressed: () {
//                 Navigator.of(context).pop();
//                 Navigator.pushReplacementNamed(context, '/main');
//               },
//             ),
//           ],
//         );
//       },
//     );
//   }

//   void _showErrorDialog(String title, [String? message]) {
//     showDialog(
//       context: context,
//       builder: (BuildContext context) {
//         return AlertDialog(
//           shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15.0)),
//           title: Row(
//             children: [
//               Icon(Icons.error, color: errorColor, size: 28),
//               const SizedBox(width: 10),
//               Text(title, style: TextStyle(color: errorColor)),
//             ],
//           ),
//           content: ConstrainedBox(
//             constraints: BoxConstraints(
//               maxHeight: MediaQuery.of(context).size.height * 0.4,
//               maxWidth: MediaQuery.of(context).size.width * 0.8,
//             ),
//             child: SingleChildScrollView(
//               child: Text(
//                 message ?? "An unexpected error occurred. Please try again later.",
//                 style: TextStyle(fontSize: 16),
//               ),
//             ),
//           ),
//           actions: <Widget>[
//             TextButton(
//               child: const Text("OK", style: TextStyle(fontSize: 16)),
//               onPressed: () {
//                 Navigator.of(context).pop();
//               },
//             ),
//           ],
//         );
//       },
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: Colors.transparent,
//       appBar: AppBar(
//         title: const Text("Popular Plans"),
//         backgroundColor: platecolor.withOpacity(0.8),
//         foregroundColor: Colors.white,
//         elevation: 0,
//       ),
//       body: AnimatedBuilder(
//         animation: _backgroundController,
//         builder: (context, child) {
//           return Container(
//             width: double.infinity,
//             height: double.infinity,
//             decoration: BoxDecoration(
//               gradient: LinearGradient(
//                 colors: const [
//                   Color(0xFF265073),
//                   Color(0xFF2D9596),
//                   Color(0xFF9AD0C2),
//                 ],
//                 begin: _gradientBegin,
//                 end: _gradientEnd,
//               ),
//             ),
//             child: child,
//           );
//         },
//         child: SafeArea(
//           child: Stack(
//             children: [
//               FadeTransition(
//                 opacity: _fadeAnimation,
//                 child: SingleChildScrollView(
//                   physics: const BouncingScrollPhysics(),
//                   child: Padding(
//                     padding: const EdgeInsets.symmetric(vertical: 20.0),
//                     child: Column(
//                       mainAxisAlignment: MainAxisAlignment.center,
//                       crossAxisAlignment: CrossAxisAlignment.center,
//                       children: [
//                         const SizedBox(height: 20),
//                         if (plans.isNotEmpty)
//                           CarouselSlider(
//                             options: CarouselOptions(
//                               height: MediaQuery.of(context).size.height * 0.6,
//                               enlargeCenterPage: true,
//                               enableInfiniteScroll: false,
//                               viewportFraction: 0.8,
//                               aspectRatio: 16 / 9,
//                               autoPlay: false,
//                             ),
//                             items: plans.map((plan) {
//                               return PlanCard(
//                                 plan: plan,
//                                 onTap: handlePayment,
//                                 currency: currency,
//                               );
//                             }).toList(),
//                           )
//                         else if (!_isLoading)
//                           const Center(
//                             child: Padding(
//                               padding: EdgeInsets.all(20.0),
//                               child: Text(
//                                 "No plans available at the moment.",
//                                 style: TextStyle(color: Colors.white70, fontSize: 16),
//                               ),
//                             ),
//                           ),
//                         const SizedBox(height: 30),
//                       ],
//                     ),
//                   ),
//                 ),
//               ),
//               Positioned(
//                 bottom: 20,
//                 right: 20,
//                 child: GestureDetector(
//                   onTap: () {
//                     Navigator.push(
//                       context,
//                       MaterialPageRoute(
//                         builder: (context) => const PrivacyPolicyScreen(),
//                       ),
//                     );
//                   },
//                   child: Container(
//                     padding: const EdgeInsets.all(8.0),
//                     decoration: BoxDecoration(
//                       shape: BoxShape.circle,
//                       color: Colors.white.withOpacity(0.2),
//                       border: Border.all(color: Colors.white.withOpacity(0.4)),
//                       boxShadow: [
//                         BoxShadow(
//                           color: Colors.black.withOpacity(0.1),
//                           blurRadius: 8,
//                           offset: const Offset(0, 4),
//                         ),
//                       ],
//                     ),
//                     child: Lottie.asset(
//                       'assets/animations/privacy_policy.json',
//                       width: 60,
//                       height: 150,
//                       fit: BoxFit.contain,
//                       repeat: true,
//                     ),
//                   ),
//                 ),
//               ),
//               if (_isLoading)
//                 Container(
//                   color: Colors.black.withOpacity(0.5),
//                   child: const Center(
//                     child: CircularProgressIndicator(
//                       valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
//                     ),
//                   ),
//                 ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }