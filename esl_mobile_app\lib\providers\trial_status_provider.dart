import 'package:flutter/material.dart';
import '../services/trial_management_service.dart';

class TrialStatusProvider extends ChangeNotifier {
  Map<String, dynamic>? _userPlan;
  bool _isLoading = false;
  DateTime? _lastFetch;
  
  // Cache duration - refresh every 5 minutes
  static const Duration _cacheDuration = Duration(minutes: 5);

  Map<String, dynamic>? get userPlan => _userPlan;
  bool get isLoading => _isLoading;

  // Check if trial is active (status is not inactive)
  bool get isTrialActive {
    if (_userPlan == null) return false;
    return _userPlan!['status'] != 'inactive';
  }

  // Check if trial is expired (status is inactive)
  bool get isTrialExpired {
    if (_userPlan == null) return false;
    return _userPlan!['status'] == 'inactive';
  }

  // Check if user has premium access
  bool get hasPremiumAccess {
    if (_userPlan == null) return false;
    return _userPlan!['plan_id'] == 'premium' || _userPlan!['plan_id'] == 'paid';
  }

  // Check if user has access to all features (status is not inactive)
  bool get hasFullAccess {
    if (_userPlan == null) return false;
    return _userPlan!['status'] != 'inactive';
  }

  // Get days remaining in trial
  int get daysRemaining {
    if (_userPlan == null) return 0;
    return TrialManagementService.getDaysRemaining(_userPlan);
  }

  // Get trial expiry date
  DateTime? get trialExpiryDate {
    if (_userPlan == null) return null;
    return TrialManagementService.getTrialExpiryDate(_userPlan);
  }

  // Features that are always allowed (even with inactive status)
  static const List<String> _allowedFeatures = [
    'home',
    'explore',
    
  ];

  // Premium features that get locked when status is inactive
  static const List<String> _premiumFeatures = [
    'speaking',
    'writing',
    'homophones',
    'idioms',
    'trainer',
    'coach',
    'chat',
    'scenario',
    'roleplay',
    'role_module'
    'vocabulary_advanced',
    'grammar_advanced',
    'pronunciation',
    'conversation_practice'
  ];

  // Check if a specific feature is accessible - CRITICAL: Based on plan status only
  bool isFeatureAccessible(String featureName) {
    final feature = featureName.toLowerCase();
    final planStatus = _userPlan?['status'] ?? 'unknown';
    final daysRemaining = _userPlan?['days_remaining'] ?? 0;

    print('Feature Access Check:');
    print('  Feature: $feature');
    print('  Plan Status: $planStatus');
    print('  Days Remaining: $daysRemaining');

    // Always allow basic features regardless of status
    if (_allowedFeatures.contains(feature)) {
      print('  Result: ALLOWED (basic feature)');
      return true;
    }

    // For premium features, check plan status
    if (_userPlan == null) {
      print('  Result: DENIED (no plan data)');
      return false;
    }

    // CRITICAL LOGIC: Feature accessibility is determined by status, NOT days remaining
    final isAccessible = _userPlan!['status'] != 'inactive';

    if (_premiumFeatures.contains(feature)) {
      print('  Result: ${isAccessible ? 'ALLOWED' : 'DENIED'} (premium feature, status-based)');
    } else {
      print('  Result: ${isAccessible ? 'ALLOWED' : 'DENIED'} (other feature, status-based)');
    }

    return isAccessible;
  }

  // Check if 24-hour reminder should be shown
  bool get should24HourReminder {
    return TrialManagementService.shouldShow24HourReminder(_userPlan);
  }

  // Get precise time until expiry
  Duration? get timeUntilExpiry {
    return TrialManagementService.getTimeUntilExpiry(_userPlan);
  }

  // Fetch trial status from API
  Future<void> fetchTrialStatus({bool forceRefresh = false}) async {
    // Check if we need to refresh based on cache duration
    if (!forceRefresh &&
        _lastFetch != null &&
        DateTime.now().difference(_lastFetch!) < _cacheDuration &&
        _userPlan != null) {
      return;
    }

    _isLoading = true;
    notifyListeners();

    try {
      final userPlan = await TrialManagementService.fetchUserPlan();
      final previousStatus = _userPlan?['status'];
      _userPlan = userPlan;
      _lastFetch = DateTime.now();

      // Log status changes
      if (userPlan != null) {
        final currentStatus = userPlan['status'];
        if (previousStatus != currentStatus) {
          print('Trial Status Changed: $previousStatus -> $currentStatus');
          if (currentStatus == 'inactive') {
            print('CRITICAL: Plan expired - features will be locked immediately');
          }
        }
      }
    } catch (e) {
      print('Error fetching trial status: $e');
      // Keep existing data if fetch fails
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Refresh trial status
  Future<void> refreshTrialStatus() async {
    await fetchTrialStatus(forceRefresh: true);
  }

  // Clear trial data (for logout)
  void clearTrialData() {
    _userPlan = null;
    _lastFetch = null;
    notifyListeners();
  }

  // Show upgrade dialog when feature is locked
  void showUpgradeDialog(BuildContext context, {String? featureName}) {
    showDialog(
      context: context,
      builder: (context) => _buildUpgradeDialog(context, featureName),
    );
  }

  Widget _buildUpgradeDialog(BuildContext context, String? featureName) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.teal.shade50,
              Colors.white,
            ],
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.teal, Colors.tealAccent],
                ),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.lock,
                color: Colors.white,
                size: 40,
              ),
            ),
            
            const SizedBox(height: 20),
            
            Text(
              isTrialExpired ? 'Trial Expired' : 'Premium Feature',
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.teal,
              ),
            ),
            
            const SizedBox(height: 12),
            
            Text(
              isTrialExpired 
                  ? 'Your trial has expired. Upgrade to premium to continue accessing all features!'
                  : featureName != null 
                      ? 'The $featureName feature requires a premium subscription.'
                      : 'This feature requires a premium subscription.',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 24),
            
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text(
                      'Maybe Later',
                      style: TextStyle(
                        color: Colors.grey[600],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                
              ],
            ),
          ],
        ),
      ),
    );
  }
}
