import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e_sasthra/constants/app_colors.dart';

class About1Page extends StatefulWidget {
  const About1Page({super.key});

  @override
  State<About1Page> createState() => _About1PageState();
}

class _About1PageState extends State<About1Page>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _floatAnimation;
  bool _showText = false;

  @override
  void initState() {
    super.initState();

    _controller =
        AnimationController(vsync: this, duration: const Duration(seconds: 3))
          ..repeat(reverse: true);

    _floatAnimation =
        Tween<double>(begin: 0, end: 20).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    Future.delayed(const Duration(milliseconds: 800), () {
      setState(() {
        _showText = true;
      });
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Widget buildImage(String path, double size) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        image: DecorationImage(
          image: AssetImage(path),
          fit: BoxFit.cover,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Background
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Color.fromARGB(255, 134, 217, 209),
                  Color.fromARGB(255, 24, 195, 178),
                  Color.fromARGB(255, 3, 127, 106),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),

          // Foreground with Scroll
          SafeArea(
            child: SingleChildScrollView(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const SizedBox(height: 20), // Add padding at the top
                    // Centered Image Stack
                    SizedBox(
                      height: 400,
                      width: 400,
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          // Background image 1 (slightly smaller)
                          buildImage('assets/images/about_3.png', 220),
                          // Background image 2 (slightly smaller)
                          buildImage('assets/about_2.png', 220),
                          // Floating Main Center Image (largest)
                          AnimatedBuilder(
                            animation: _floatAnimation,
                            builder: (context, child) {
                              return Transform.translate(
                                offset: Offset(0, -_floatAnimation.value),
                                child: buildImage('assets/about_1.png', 280),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 40),

                    // Fade-in Text
                    AnimatedOpacity(
                      opacity: _showText ? 1 : 0,
                      duration: const Duration(seconds: 2),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        child: Text(
                          'Speak Confidently, Learn Effortlessly,\nPowered by AI!',
                          textAlign: TextAlign.center,
                          style: GoogleFonts.rubik(
                            fontSize: 28,
                            fontWeight: FontWeight.w800,
                            color: AppColors.white,
                            shadows: [
                              Shadow(
                                offset: const Offset(2, 2),
                                blurRadius: 6,
                                color: Colors.black.withOpacity(0.4),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 20), // Add padding at the bottom
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}