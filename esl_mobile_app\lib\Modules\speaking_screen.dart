// import 'package:flutter/material.dart';
// import 'package:http/http.dart' as http;
// import 'dart:convert';
// import 'package:record/record.dart';
// import 'package:permission_handler/permission_handler.dart';
// import 'package:path_provider/path_provider.dart';
// import 'dart:io';
// import 'dart:async';
// import 'dart:math';

// class SpeakingPlayground extends StatefulWidget {
//   const SpeakingPlayground({super.key});

//   @override
//   _SpeakingPlaygroundState createState() => _SpeakingPlaygroundState();
// }

// class _SpeakingPlaygroundState extends State<SpeakingPlayground>
//     with TickerProviderStateMixin {
//   String? selectedTheme;
//   Map<String, String>? generatedQuestion;
//   bool recording = false;
//   Map<String, dynamic>? evaluation;
//   String transcription = '';
//   String languageError = '';
//   bool isBackButtonDisabled = false;
//   bool isEvaluating = false;
//   final String staticUserId = '148';
//   bool isCardFlipped = false;

//   AudioRecorder? _record;
//   bool _isRecorderInitialized = false;
//   String? _recordingFilePath;
//   Timer? _recordingTimer;
//   int _recordingDuration = 0;

//   final Map<String, String> themeImages = {
//     'Work and Career': 'https://ppt-to-pdf-dsais.s3.eu-north-1.amazonaws.com/ESL_img/ESL_speakingmodule_img/work.jpg',
//     'Education and Study': 'https://ppt-to-pdf-dsais.s3.eu-north-1.amazonaws.com/ESL_img/ESL_speakingmodule_img/education.jpg',
//     'Hometown and Neighborhood': 'https://ppt-to-pdf-dsais.s3.eu-north-1.amazonaws.com/ESL_img/ESL_speakingmodule_img/hometown.jpg',
//     'Home and Accommodation': 'https://ppt-to-pdf-dsais.s3.eu-north-1.amazonaws.com/ESL_img/ESL_speakingmodule_img/home.jpg',
//     'Hobbies and Interests': 'https://ppt-to-pdf-dsais.s3.eu-north-1.amazonaws.com/ESL_img/ESL_speakingmodule_img/hobbies.jpg',
//     'Family and Relationships': 'https://ppt-to-pdf-dsais.s3.eu-north-1.amazonaws.com/ESL_img/ESL_speakingmodule_img/family.jpg',
//     'Travel and Tourism': 'https://ppt-to-pdf-dsais.s3.eu-north-1.amazonaws.com/ESL_img/ESL_speakingmodule_img/travel.jpg',
//     'Technology and Gadgets': 'https://ppt-to-pdf-dsais.s3.eu-north-1.amazonaws.com/ESL_img/ESL_speakingmodule_img/technology.jpg',
//     'Food and Dining': 'https://ppt-to-pdf-dsais.s3.eu-north-1.amazonaws.com/ESL_img/ESL_speakingmodule_img/food.jpg',
//   };

//   final GlobalKey themeSectionKey = GlobalKey();
//   final GlobalKey questionSectionKey = GlobalKey();
//   final GlobalKey recordSectionKey = GlobalKey();
//   final GlobalKey evaluationSectionKey = GlobalKey();

//   final ScrollController _scrollController = ScrollController();
//   final PageController _pageController = PageController(viewportFraction: 0.8);

//   late AnimationController _fadeController;
//   late Animation<double> _fadeAnimation;
//   late AnimationController _flipController;
//   late Animation<double> _flipAnimation;

//   @override
//   void initState() {
//     super.initState();
//     _initRecorder();
//     _fadeController = AnimationController(
//         vsync: this, duration: const Duration(milliseconds: 500));
//     _fadeAnimation =
//         CurvedAnimation(parent: _fadeController, curve: Curves.easeIn);

//     _flipController = AnimationController(
//       vsync: this,
//       duration: const Duration(milliseconds: 600),
//     );
//     _flipAnimation = Tween<double>(begin: 0, end: 1).animate(
//       CurvedAnimation(
//         parent: _flipController,
//         curve: Curves.easeInOut,
//       ),
//     );
//   }

//   Future<void> _initRecorder() async {
//     var status = await Permission.microphone.request();
//     if (status != PermissionStatus.granted) {
//       setState(() {
//         languageError = 'Microphone permission not granted';
//       });
//       return;
//     }

//     _record = AudioRecorder();
//     setState(() {
//       _isRecorderInitialized = true;
//     });
//   }

//   @override
//   void dispose() {
//     _recordingTimer?.cancel();
//     _record?.dispose();
//     _fadeController.dispose();
//     _flipController.dispose();
//     _scrollController.dispose();
//     _pageController.dispose();
//     super.dispose();
//   }

//   Future<String> getDocumentPath() async {
//     final directory = await getApplicationDocumentsDirectory();
//     return directory.path;
//   }

//   void _scrollToSection(GlobalKey key) {
//     final context = key.currentContext;
//     if (context != null) {
//       Scrollable.ensureVisible(
//         context,
//         duration: const Duration(milliseconds: 500),
//         curve: Curves.easeInOut,
//       );
//     }
//   }

//   Future<void> _generateQuestion() async {
//     if (selectedTheme == null) {
//       setState(() {
//         languageError = 'Please select a theme first!';
//       });
//       return;
//     }

//     try {
//       final response = await http.get(
//         Uri.parse(
//             'https://talktoai.in/speaking/generate_question?theme_name=$selectedTheme&user_id=$staticUserId'),
//       );
//       final data = json.decode(response.body);
//       if (data['error'] != null) {
//         setState(() {
//           languageError = data['error'];
//         });
//       } else {
//         setState(() {
//           generatedQuestion = {
//             'question': data['question'],
//             'suggestedAnswer': data['suggested_answer'],
//           };
//           evaluation = null;
//           transcription = '';
//           languageError = '';
//           isCardFlipped = false;
//           recording = false;
//           isEvaluating = false;
//           _recordingDuration = 0;
//         });
//         Future.delayed(const Duration(milliseconds: 200), () {
//           _scrollToSection(recordSectionKey);
//         });
//       }
//     } catch (error) {
//       setState(() {
//         languageError = 'Error generating question: $error';
//       });
//     }
//   }

//   Future<void> _startRecording() async {
//     if (!_isRecorderInitialized || _record == null) {
//       setState(() {
//         languageError = 'Recorder not initialized';
//       });
//       return;
//     }
//     if (generatedQuestion == null) {
//       setState(() {
//         languageError = 'Please generate a question first before recording.';
//       });
//       return;
//     }
//     if (await Permission.microphone.status != PermissionStatus.granted) {
//       setState(() {
//         languageError = 'Microphone permission not granted';
//       });
//       return;
//     }

//     setState(() {
//       recording = true;
//       languageError = '';
//       _recordingDuration = 0;
//       transcription = '';
//       evaluation = null;
//     });

//     try {
//       _recordingFilePath = '${await getDocumentPath()}/audio_${DateTime.now().millisecondsSinceEpoch}.wav';
//       await _record!.start(
//         const RecordConfig(
//           encoder: AudioEncoder.wav,
//           bitRate: 128000,
//           sampleRate: 44100,
//           numChannels: 1,
//         ),
//         path: _recordingFilePath!,
//       );
//       _recordingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
//         if (mounted) {
//           setState(() {
//             _recordingDuration++;
//           });
//         }
//       });
//     } catch (e) {
//       setState(() {
//         languageError = 'Error starting recording: $e';
//         recording = false;
//       });
//     }
//   }

//   Future<void> _stopRecording() async {
//     _recordingTimer?.cancel();
//     if (_record == null || _recordingFilePath == null) {
//       setState(() {
//         languageError = 'Recorder or file path not available';
//         isEvaluating = false;
//         recording = false;
//       });
//       return;
//     }

//     try {
//       final path = await _record!.stop();
//       if (path == null) {
//         setState(() {
//           languageError = 'Failed to stop recording - no audio file created';
//           isEvaluating = false;
//           recording = false;
//         });
//         return;
//       }

//       final file = File(path);
//       if (!await file.exists()) {
//         setState(() {
//           languageError = 'Audio file does not exist at path: $path';
//           isEvaluating = false;
//           recording = false;
//         });
//         return;
//       }

//       final length = await file.length();
//       if (length < 1000) {
//         setState(() {
//           languageError = 'Recorded audio file is too small ($length bytes)';
//           isEvaluating = false;
//           recording = false;
//         });
//         return;
//       }

//       setState(() {
//         recording = false;
//         isEvaluating = true;
//       });
      
//       await _sendAudio(path);
//     } catch (e) {
//       setState(() {
//         languageError = 'Error stopping recording: $e';
//         isEvaluating = false;
//         recording = false;
//       });
//     }
//   }

//   Future<void> _sendAudio(String path) async {
//     try {
//       final uri = Uri.parse('https://talktoai.in/speaking/capture_speech');
//       final request = http.MultipartRequest('POST', uri);
//       request.files.add(await http.MultipartFile.fromPath('audio', path));

//       final response = await request.send();
//       final respStr = await response.stream.bytesToString();

//       if (response.statusCode == 200) {
//         final data = json.decode(respStr);
//         if (data['error'] != null) {
//           if (mounted) {
//             setState(() {
//               languageError = data['error'];
//               isEvaluating = false;
//             });
//           }
//         } else {
//           if (mounted) {
//             setState(() {
//               transcription = data['transcription'] ?? '';
//             });
//           }
//           await _evaluateAnswer(transcription);
//         }
//       } else {
//         if (mounted) {
//           setState(() {
//             languageError = 'Server error: ${response.statusCode} - $respStr';
//             isEvaluating = false;
//           });
//         }
//       }
//     } catch (error) {
//       if (mounted) {
//         setState(() {
//           languageError = 'Failed to send audio: $error';
//           isEvaluating = false;
//         });
//       }
//     }
//   }

//   Future<void> _evaluateAnswer(String sentence) async {
//     if (sentence.isEmpty || generatedQuestion == null || selectedTheme == null) {
//       setState(() {
//         languageError = 'Cannot evaluate empty answer';
//         isEvaluating = false;
//       });
//       return;
//     }

//     try {
//       final response = await http.post(
//         Uri.parse('https://talktoai.in/speaking/evaluate_answer'),
//         headers: {'Content-Type': 'application/json'},
//         body: json.encode({
//           'sentence': sentence,
//           'theme_name': selectedTheme,
//           'user_id': staticUserId,
//           'question': generatedQuestion!['question'],
//         }),
//       );
      
//       if (response.statusCode == 200) {
//         final data = json.decode(response.body);
//         if (data['error'] != null) {
//           if (mounted) {
//             setState(() {
//               languageError = data['error'];
//               isEvaluating = false;
//             });
//           }
//         } else {
//           if (mounted) {
//             setState(() {
//               evaluation = data;
//               isEvaluating = false;
//             });
//             _fadeController.forward(from: 0.0);
//             Future.delayed(const Duration(milliseconds: 200), () {
//               if (mounted) {
//                 _scrollToSection(evaluationSectionKey);
//               }
//             });
//           }
//         }
//       } else {
//         if (mounted) {
//           setState(() {
//             languageError = 'Evaluation failed: ${response.statusCode}';
//             isEvaluating = false;
//           });
//         }
//       }
//     } catch (error) {
//       if (mounted) {
//         setState(() {
//           languageError = 'Error evaluating answer: $error';
//           isEvaluating = false;
//         });
//       }
//     }
//   }

//   String cleanFeedback(String feedback) {
//     return feedback.replaceAll(RegExp(r'\*\*|###'), '').trim();
//   }

//   void _toggleCardFlip() {
//     if (_flipController.isCompleted) {
//       _flipController.reverse();
//     } else {
//       _flipController.forward();
//     }
//     setState(() {
//       isCardFlipped = !isCardFlipped;
//     });
//   }

//   Widget _buildThemeCard(String theme, BuildContext context) {
//     return Container(
//       margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 16),
//       child: GestureDetector(
//         onTap: recording
//             ? null
//             : () {
//                 setState(() {
//                   selectedTheme = theme;
//                   generatedQuestion = null;
//                   evaluation = null;
//                   transcription = '';
//                   languageError = '';
//                 });
//               },
//         child: Card(
//           elevation: 8,
//           shape: RoundedRectangleBorder(
//             borderRadius: BorderRadius.circular(20),
//           ),
//           clipBehavior: Clip.antiAlias,
//           child: Stack(
//             children: [
//               Positioned.fill(
//                 child: Image.network(
//                   themeImages[theme]!,
//                   fit: BoxFit.cover,
//                   loadingBuilder: (context, child, loadingProgress) {
//                     if (loadingProgress == null) return child;
//                     return Center(
//                       child: CircularProgressIndicator(
//                         value: loadingProgress.expectedTotalBytes != null
//                             ? loadingProgress.cumulativeBytesLoaded /
//                                 loadingProgress.expectedTotalBytes!
//                             : null,
//                       ),
//                     );
//                   },
//                   errorBuilder: (context, error, stackTrace) {
//                     return Container(
//                       color: Colors.grey[200],
//                       child: Center(
//                         child: Column(
//                           mainAxisAlignment: MainAxisAlignment.center,
//                           children: [
//                             const Icon(Icons.broken_image, size: 48),
//                             const SizedBox(height: 16),
//                             Text(
//                               theme,
//                               style: const TextStyle(
//                                 fontSize: 24,
//                                 fontWeight: FontWeight.bold,
//                               ),
//                             ),
//                           ],
//                         ),
//                       ),
//                     );
//                   },
//                 ),
//               ),
//               Positioned.fill(
//                 child: Container(
//                   decoration: BoxDecoration(
//                     gradient: LinearGradient(
//                       begin: Alignment.bottomCenter,
//                       end: Alignment.topCenter,
//                       colors: [
//                         Colors.black.withOpacity(0.8),
//                         Colors.transparent,
//                       ],
//                     ),
//                   ),
//                 ),
//               ),
//               Positioned(
//                 bottom: 32,
//                 left: 24,
//                 right: 24,
//                 child: Text(
//                   theme,
//                   style: const TextStyle(
//                     color: Colors.white,
//                     fontSize: 28,
//                     fontWeight: FontWeight.bold,
//                     shadows: [
//                       Shadow(
//                         blurRadius: 10,
//                         color: Colors.black,
//                         offset: Offset(2, 2),
//                       )
//                     ],
//                   ),
//                 ),
//               ),
//               if (selectedTheme == theme)
//                 Positioned(
//                   top: 16,
//                   right: 16,
//                   child: Container(
//                     padding: const EdgeInsets.all(8),
//                     decoration: BoxDecoration(
//                       color: Colors.white,
//                       shape: BoxShape.circle,
//                     ),
//                     child: const Icon(
//                       Icons.check_circle,
//                       color: Colors.green,
//                       size: 36,
//                     ),
//                   ),
//                 ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     final screenHeight = MediaQuery.of(context).size.height;
//     final cardHeight = screenHeight * 0.5;

//     return Scaffold(
//       backgroundColor: Colors.white,
//       appBar: AppBar(
//         title: const Text('Speaking Playground'),
//         backgroundColor: Colors.white,
//         elevation: 0,
//         centerTitle: true,
//         leading: IconButton(
//           icon: const Icon(Icons.arrow_back, color: Colors.black),
//           onPressed: isBackButtonDisabled
//               ? null
//               : () {
//                   setState(() {
//                     isBackButtonDisabled = true;
//                   });
//                   Navigator.of(context).pop();
//                 },
//         ),
//       ),
//       body: SingleChildScrollView(
//         controller: _scrollController,
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             Padding(
//               padding: const EdgeInsets.all(16.0),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   const Text(
//                     'Select a Theme',
//                     style: TextStyle(
//                       fontSize: 28,
//                       fontWeight: FontWeight.bold,
//                     ),
//                   ),
//                   const SizedBox(height: 8),
//                   const Text(
//                     'Swipe horizontally to browse topics',
//                     style: TextStyle(
//                       color: Colors.grey,
//                       fontSize: 16,
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//             SizedBox(
//               height: cardHeight,
//               child: PageView.builder(
//                 controller: _pageController,
//                 itemCount: themeImages.length,
//                 itemBuilder: (context, index) {
//                   final theme = themeImages.keys.elementAt(index);
//                   return _buildThemeCard(theme, context);
//                 },
//                 onPageChanged: (index) {
//                   final theme = themeImages.keys.elementAt(index);
//                   setState(() {
//                     selectedTheme = theme;
//                     generatedQuestion = null;
//                     evaluation = null;
//                     transcription = '';
//                     languageError = '';
//                   });
//                 },
//               ),
//             ),
//             const SizedBox(height: 24),
//             if (selectedTheme != null)
//               Padding(
//                 padding: const EdgeInsets.all(16.0),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     const Text(
//                       'Practice Question',
//                       style: TextStyle(
//                         fontSize: 24,
//                         fontWeight: FontWeight.bold,
//                       ),
//                     ),
//                     const SizedBox(height: 16),
//                     ElevatedButton(
//                       onPressed: _generateQuestion,
//                       style: ElevatedButton.styleFrom(
//                         backgroundColor: Colors.blue,
//                         shape: RoundedRectangleBorder(
//                           borderRadius: BorderRadius.circular(12),
//                         ),
//                         padding: const EdgeInsets.symmetric(
//                             horizontal: 24, vertical: 16),
//                       ),
//                       child: const Text(
//                         'Generate Question',
//                         style: TextStyle(
//                           color: Colors.white,
//                           fontSize: 16,
//                         ),
//                       ),
//                     ),
//                     const SizedBox(height: 24),
//                     AnimatedSwitcher(
//                       duration: const Duration(milliseconds: 300),
//                       child: generatedQuestion != null
//                           ? GestureDetector(
//                               onTap: _toggleCardFlip,
//                               child: AnimatedBuilder(
//                                 animation: _flipAnimation,
//                                 builder: (context, child) {
//                                   final angle = _flipAnimation.value * 3.14159;
//                                   final transform = Matrix4.identity()
//                                     ..setEntry(3, 2, 0.001)
//                                     ..rotateY(angle);
//                                   return Transform(
//                                     transform: transform,
//                                     alignment: Alignment.center,
//                                     child: Container(
//                                       key: const ValueKey('generatedQuestion'),
//                                       width: double.infinity,
//                                       padding: const EdgeInsets.all(24),
//                                       decoration: BoxDecoration(
//                                         color: isCardFlipped
//                                             ? Colors.grey[100]
//                                             : Colors.white,
//                                         borderRadius: BorderRadius.circular(16),
//                                         boxShadow: [
//                                           BoxShadow(
//                                             color: Colors.grey.withOpacity(0.1),
//                                             spreadRadius: 2,
//                                             blurRadius: 8,
//                                             offset: const Offset(0, 4),
//                                           ),
//                                         ],
//                                         border: Border.all(
//                                           color: Colors.grey[200]!,
//                                         ),
//                                       ),
//                                       child: isCardFlipped
//                                           ? Column(
//                                               crossAxisAlignment:
//                                                   CrossAxisAlignment.start,
//                                               children: [
//                                                 const Text(
//                                                   'Suggested Answer',
//                                                   style: TextStyle(
//                                                     fontWeight: FontWeight.bold,
//                                                     fontSize: 18,
//                                                     color: Colors.blue,
//                                                   ),
//                                                 ),
//                                                 const SizedBox(height: 16),
//                                                 Text(
//                                                   generatedQuestion![
//                                                           'suggestedAnswer'] ??
//                                                       '',
//                                                   style: const TextStyle(
//                                                     fontSize: 16,
//                                                     color: Colors.black87,
//                                                   ),
//                                                 ),
//                                                 const SizedBox(height: 16),
//                                                 const Icon(Icons.flip,
//                                                     color: Colors.blue),
//                                               ],
//                                             )
//                                           : Column(
//                                               crossAxisAlignment:
//                                                   CrossAxisAlignment.start,
//                                               children: [
//                                                 const Text(
//                                                   'Question',
//                                                   style: TextStyle(
//                                                     fontWeight: FontWeight.bold,
//                                                     fontSize: 18,
//                                                     color: Colors.blue,
//                                                   ),
//                                                 ),
//                                                 const SizedBox(height: 16),
//                                                 Text(
//                                                   generatedQuestion![
//                                                           'question'] ??
//                                                       '',
//                                                   style: const TextStyle(
//                                                     fontSize: 16,
//                                                     color: Colors.black87,
//                                                   ),
//                                                 ),
//                                                 const SizedBox(height: 16),
//                                                 const Icon(Icons.flip,
//                                                     color: Colors.blue),
//                                               ],
//                                             ),
//                                     ),
//                                   );
//                                 },
//                               ),
//                             )
//                           : Container(
//                               padding: const EdgeInsets.all(24),
//                               decoration: BoxDecoration(
//                                 color: Colors.grey[50],
//                                 borderRadius: BorderRadius.circular(16),
//                                 border: Border.all(
//                                   color: Colors.grey[200]!,
//                                 ),
//                               ),
//                               child: const Center(
//                                 child: Text(
//                                   'Generate a question to get started',
//                                   style: TextStyle(
//                                     color: Colors.grey,
//                                     fontSize: 16,
//                                   ),
//                                 ),
//                               ),
//                             ),
//                     ),
//                   ],
//                 ),
//               ),
//             if (generatedQuestion != null) ...[
//               const SizedBox(height: 24),
//               Padding(
//                 padding: const EdgeInsets.all(16.0),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     const Text(
//                       'Record Your Answer',
//                       style: TextStyle(
//                         fontSize: 24,
//                         fontWeight: FontWeight.bold,
//                       ),
//                     ),
//                     const SizedBox(height: 16),
//                     Row(
//                       mainAxisAlignment: MainAxisAlignment.center,
//                       children: [
//                         ElevatedButton(
//                           onPressed: recording ? null : _startRecording,
//                           style: ElevatedButton.styleFrom(
//                             backgroundColor:
//                                 recording ? Colors.red : Colors.blue,
//                             shape: RoundedRectangleBorder(
//                               borderRadius: BorderRadius.circular(12),
//                             ),
//                             padding: const EdgeInsets.symmetric(
//                                 horizontal: 24, vertical: 16),
//                           ),
//                           child: Row(
//                             mainAxisSize: MainAxisSize.min,
//                             children: [
//                               Icon(
//                                 recording ? Icons.mic : Icons.mic_none,
//                                 color: Colors.white,
//                               ),
//                               const SizedBox(width: 8),
//                               Text(
//                                 recording
//                                     ? 'Recording ($_recordingDuration s)'
//                                     : 'Start Recording',
//                                 style: const TextStyle(
//                                   color: Colors.white,
//                                   fontSize: 16,
//                                 ),
//                               ),
//                             ],
//                           ),
//                         ),
//                         if (recording) ...[
//                           const SizedBox(width: 16),
//                           ElevatedButton(
//                             onPressed: _stopRecording,
//                             style: ElevatedButton.styleFrom(
//                               backgroundColor: Colors.red,
//                               shape: RoundedRectangleBorder(
//                                 borderRadius: BorderRadius.circular(12),
//                               ),
//                               padding: const EdgeInsets.symmetric(
//                                   horizontal: 24, vertical: 16),
//                             ),
//                             child: const Row(
//                               mainAxisSize: MainAxisSize.min,
//                               children: [
//                                 Icon(Icons.stop, color: Colors.white),
//                                 SizedBox(width: 8),
//                                 Text(
//                                   'Stop',
//                                   style: TextStyle(
//                                     color: Colors.white,
//                                     fontSize: 16,
//                                   ),
//                                 ),
//                               ],
//                             ),
//                           ),
//                         ],
//                       ],
//                     ),
//                     const SizedBox(height: 24),
//                     if (isEvaluating)
//                       const Column(
//                         children: [
//                           CircularProgressIndicator(
//                             valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
//                           ),
//                           SizedBox(height: 16),
//                           Text(
//                             'Processing your answer...',
//                             style: TextStyle(
//                               color: Colors.grey,
//                               fontSize: 16,
//                             ),
//                           ),
//                         ],
//                       ),
//                     if (!recording &&
//                         transcription.isEmpty &&
//                         languageError.isEmpty &&
//                         !isEvaluating)
//                       const Center(
//                         child: Text(
//                           'Press the microphone button to record your answer',
//                           style: TextStyle(
//                             color: Colors.grey,
//                             fontSize: 16,
//                           ),
//                         ),
//                       ),
//                     if (transcription.isNotEmpty)
//                       Container(
//                         width: double.infinity,
//                         padding: const EdgeInsets.all(16),
//                         decoration: BoxDecoration(
//                           color: Colors.grey[50],
//                           borderRadius: BorderRadius.circular(16),
//                           border: Border.all(
//                             color: Colors.grey[200]!,
//                           ),
//                         ),
//                         child: Column(
//                           crossAxisAlignment: CrossAxisAlignment.start,
//                           children: [
//                             const Text(
//                               'Your Answer',
//                               style: TextStyle(
//                                 fontWeight: FontWeight.bold,
//                                 fontSize: 18,
//                                 color: Colors.blue,
//                               ),
//                             ),
//                             const SizedBox(height: 8),
//                             Text(
//                               transcription,
//                               style: const TextStyle(
//                                 fontSize: 16,
//                                 color: Colors.black87,
//                               ),
//                             ),
//                           ],
//                         ),
//                       ),
//                     if (languageError.isNotEmpty)
//                       Container(
//                         width: double.infinity,
//                         margin: const EdgeInsets.only(top: 16),
//                         padding: const EdgeInsets.all(16),
//                         decoration: BoxDecoration(
//                           color: Colors.red[50],
//                           borderRadius: BorderRadius.circular(16),
//                           border: Border.all(
//                             color: Colors.red[200]!,
//                           ),
//                         ),
//                         child: Text(
//                           languageError,
//                           style: const TextStyle(
//                             color: Colors.red,
//                             fontSize: 16,
//                           ),
//                         ),
//                       ),
//                   ],
//                 ),
//               ),
//             ],
//             if (evaluation != null) ...[
//               const SizedBox(height: 24),
//               FadeTransition(
//                 opacity: _fadeAnimation,
//                 child: Padding(
//                   padding: const EdgeInsets.all(16.0),
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       const Text(
//                         'Evaluation Results',
//                         style: TextStyle(
//                           fontSize: 24,
//                           fontWeight: FontWeight.bold,
//                         ),
//                       ),
//                       const SizedBox(height: 16),
//                       Container(
//                         width: double.infinity,
//                         padding: const EdgeInsets.all(16),
//                         decoration: BoxDecoration(
//                           color: Colors.green[50],
//                           borderRadius: BorderRadius.circular(16),
//                           border: Border.all(
//                             color: Colors.green[200]!,
//                           ),
//                         ),
//                         child: Column(
//                           crossAxisAlignment: CrossAxisAlignment.start,
//                           children: [
//                             const Text(
//                               'Feedback',
//                               style: TextStyle(
//                                 fontWeight: FontWeight.bold,
//                                 fontSize: 18,
//                                 color: Colors.green,
//                               ),
//                             ),
//                             const SizedBox(height: 8),
//                             ...cleanFeedback(evaluation!['feedback'] as String)
//                                 .split('\n\n')
//                                 .map((item) => Padding(
//                                       padding: const EdgeInsets.only(top: 8.0),
//                                       child: Text(
//                                         item,
//                                         style: const TextStyle(
//                                           fontSize: 16,
//                                           color: Colors.black87,
//                                         ),
//                                       ),
//                                     )),
//                           ],
//                         ),
//                       ),
//                       const SizedBox(height: 16),
//                       if (evaluation!['suggestions'] != null)
//                         Container(
//                           width: double.infinity,
//                           padding: const EdgeInsets.all(16),
//                           decoration: BoxDecoration(
//                             color: Colors.blue[50],
//                             borderRadius: BorderRadius.circular(16),
//                             border: Border.all(
//                               color: Colors.blue[200]!,
//                             ),
//                           ),
//                           child: Column(
//                             crossAxisAlignment: CrossAxisAlignment.start,
//                             children: [
//                               const Text(
//                                 'Suggestions for Improvement',
//                                 style: TextStyle(
//                                   fontWeight: FontWeight.bold,
//                                   fontSize: 18,
//                                   color: Colors.blue,
//                                 ),
//                               ),
//                               const SizedBox(height: 8),
//                               ...(evaluation!['suggestions'] as List<dynamic>)
//                                   .map<Widget>((suggestion) => Padding(
//                                         padding:
//                                             const EdgeInsets.only(top: 8.0),
//                                         child: Row(
//                                           crossAxisAlignment:
//                                               CrossAxisAlignment.start,
//                                           children: [
//                                             const Icon(
//                                               Icons.arrow_right,
//                                               color: Colors.blue,
//                                               size: 24,
//                                             ),
//                                             const SizedBox(width: 8),
//                                             Expanded(
//                                               child: Text(
//                                                 suggestion.toString(),
//                                                 style: const TextStyle(
//                                                   fontSize: 16,
//                                                   color: Colors.black87,
//                                                 ),
//                                               ),
//                                             ),
//                                           ],
//                                         ),
//                                       ))
//                                   .toList(),
//                             ],
//                           ),
//                         ),
//                     ],
//                   ),
//                 ),
//               ),
//             ],
//             const SizedBox(height: 40),
//           ],
//         ),
//       ),
//     );
//   }
// }

// void main() {
//   runApp(
//     MaterialApp(
//       debugShowCheckedModeBanner: false,
//       theme: ThemeData(
//         primarySwatch: Colors.blue,
//         scaffoldBackgroundColor: Colors.white,
//         fontFamily: 'Roboto',
//       ),
//       home: const SpeakingPlayground(),
//     ),
//   );
// }

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:record/record.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'dart:async';

class SpeakingPlayground extends StatefulWidget {
  const SpeakingPlayground({super.key});

  @override
  _SpeakingPlaygroundState createState() => _SpeakingPlaygroundState();
}

class _SpeakingPlaygroundState extends State<SpeakingPlayground>
    with TickerProviderStateMixin {
  String? selectedTheme;
  Map<String, String>? generatedQuestion;
  bool recording = false;
  Map<String, dynamic>? evaluation;
  String transcription = '';
  String languageError = '';
  bool isBackButtonDisabled = false;
  bool isEvaluating = false;
  final String staticUserId = '148';
  bool showSuggestedAnswer = false; // New state to toggle suggested answer

  AudioRecorder? _record;
  bool _isRecorderInitialized = false;
  String? _recordingFilePath;
  Timer? _recordingTimer;
  int _recordingDuration = 0;

  final Map<String, String> themeImages = {
    'Work and Career': 'https://ppt-to-pdf-dsais.s3.eu-north-1.amazonaws.com/ESL_img/ESL_speakingmodule_img/work.jpg',
    'Education and Study': 'https://ppt-to-pdf-dsais.s3.eu-north-1.amazonaws.com/ESL_img/ESL_speakingmodule_img/education.jpg',
    'Hometown and Neighborhood': 'https://ppt-to-pdf-dsais.s3.eu-north-1.amazonaws.com/ESL_img/ESL_speakingmodule_img/hometown.jpg',
    'Home and Accommodation': 'https://ppt-to-pdf-dsais.s3.eu-north-1.amazonaws.com/ESL_img/ESL_speakingmodule_img/home.jpg',
    'Hobbies and Interests': 'https://ppt-to-pdf-dsais.s3.eu-north-1.amazonaws.com/ESL_img/ESL_speakingmodule_img/hobbies.jpg',
    'Family and Relationships': 'https://ppt-to-pdf-dsais.s3.eu-north-1.amazonaws.com/ESL_img/ESL_speakingmodule_img/family.jpg',
    'Travel and Tourism': 'https://ppt-to-pdf-dsais.s3.eu-north-1.amazonaws.com/ESL_img/ESL_speakingmodule_img/travel.jpg',
    'Technology and Gadgets': 'https://ppt-to-pdf-dsais.s3.eu-north-1.amazonaws.com/ESL_img/ESL_speakingmodule_img/technology.jpg',
    'Food and Dining': 'https://ppt-to-pdf-dsais.s3.eu-north-1.amazonaws.com/ESL_img/ESL_speakingmodule_img/food.jpg',
  };

  final GlobalKey themeSectionKey = GlobalKey();
  final GlobalKey questionSectionKey = GlobalKey();
  final GlobalKey recordSectionKey = GlobalKey();
  final GlobalKey evaluationSectionKey = GlobalKey();

  final ScrollController _scrollController = ScrollController();
  final PageController _pageController = PageController(viewportFraction: 0.8);

  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _initRecorder();
    _fadeController = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 500));
    _fadeAnimation =
        CurvedAnimation(parent: _fadeController, curve: Curves.easeIn);
  }

  Future<void> _initRecorder() async {
    var status = await Permission.microphone.request();
    if (status != PermissionStatus.granted) {
      setState(() {
        languageError = 'Microphone permission not granted';
      });
      return;
    }

    _record = AudioRecorder();
    setState(() {
      _isRecorderInitialized = true;
    });
  }

  @override
  void dispose() {
    _recordingTimer?.cancel();
    _record?.dispose();
    _fadeController.dispose();
    _scrollController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  Future<String> getDocumentPath() async {
    final directory = await getApplicationDocumentsDirectory();
    return directory.path;
  }

  void _scrollToSection(GlobalKey key) {
    final context = key.currentContext;
    if (context != null) {
      Scrollable.ensureVisible(
        context,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  Future<void> _generateQuestion() async {
    if (selectedTheme == null) {
      setState(() {
        languageError = 'Please select a theme first!';
      });
      return;
    }

    try {
      final response = await http.get(
        Uri.parse(
            'https://talktoai.in/speaking/generate_question?theme_name=$selectedTheme&user_id=$staticUserId'),
      );
      final data = json.decode(response.body);
      if (data['error'] != null) {
        setState(() {
          languageError = data['error'];
        });
      } else {
        setState(() {
          generatedQuestion = {
            'question': data['question'],
            'suggestedAnswer': data['suggested_answer'],
          };
          evaluation = null;
          transcription = '';
          languageError = '';
          showSuggestedAnswer = false; // Reset suggested answer visibility
          recording = false;
          isEvaluating = false;
          _recordingDuration = 0;
        });
        Future.delayed(const Duration(milliseconds: 200), () {
          _scrollToSection(recordSectionKey);
        });
      }
    } catch (error) {
      setState(() {
        languageError = 'Error generating question: $error';
      });
    }
  }

  Future<void> _startRecording() async {
    if (!_isRecorderInitialized || _record == null) {
      setState(() {
        languageError = 'Recorder not initialized';
      });
      return;
    }
    if (generatedQuestion == null) {
      setState(() {
        languageError = 'Please generate a question first before recording.';
      });
      return;
    }
    if (await Permission.microphone.status != PermissionStatus.granted) {
      setState(() {
        languageError = 'Microphone permission not granted';
      });
      return;
    }

    setState(() {
      recording = true;
      languageError = '';
      _recordingDuration = 0;
      transcription = '';
      evaluation = null;
    });

    try {
      _recordingFilePath = '${await getDocumentPath()}/audio_${DateTime.now().millisecondsSinceEpoch}.wav';
      await _record!.start(
        const RecordConfig(
          encoder: AudioEncoder.wav,
          bitRate: 128000,
          sampleRate: 44100,
          numChannels: 1,
        ),
        path: _recordingFilePath!,
      );
      _recordingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        if (mounted) {
          setState(() {
            _recordingDuration++;
          });
        }
      });
    } catch (e) {
      setState(() {
        languageError = 'Error starting recording: $e';
        recording = false;
      });
    }
  }

  Future<void> _stopRecording() async {
    _recordingTimer?.cancel();
    if (_record == null || _recordingFilePath == null) {
      setState(() {
        languageError = 'Recorder or file path not available';
        isEvaluating = false;
        recording = false;
      });
      return;
    }

    try {
      final path = await _record!.stop();
      if (path == null) {
        setState(() {
          languageError = 'Failed to stop recording - no audio file created';
          isEvaluating = false;
          recording = false;
        });
        return;
      }

      final file = File(path);
      if (!await file.exists()) {
        setState(() {
          languageError = 'Audio file does not exist at path: $path';
          isEvaluating = false;
          recording = false;
        });
        return;
      }

      final length = await file.length();
      if (length < 1000) {
        setState(() {
          languageError = 'Recorded audio file is too small ($length bytes)';
          isEvaluating = false;
          recording = false;
        });
        return;
      }

      setState(() {
        recording = false;
        isEvaluating = true;
      });

      await _sendAudio(path);
    } catch (e) {
      setState(() {
        languageError = 'Error stopping recording: $e';
        isEvaluating = false;
        recording = false;
      });
    }
  }

  Future<void> _sendAudio(String path) async {
    try {
      final uri = Uri.parse('https://talktoai.in/speaking/capture_speech');
      final request = http.MultipartRequest('POST', uri);
      request.files.add(await http.MultipartFile.fromPath('audio', path));

      final response = await request.send();
      final respStr = await response.stream.bytesToString();

      if (response.statusCode == 200) {
        final data = json.decode(respStr);
        if (data['error'] != null) {
          if (mounted) {
            setState(() {
              languageError = data['error'];
              isEvaluating = false;
            });
          }
        } else {
          if (mounted) {
            setState(() {
              transcription = data['transcription'] ?? '';
            });
          }
          await _evaluateAnswer(transcription);
        }
      } else {
        if (mounted) {
          setState(() {
            languageError = 'Server error: ${response.statusCode} - $respStr';
            isEvaluating = false;
          });
        }
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          languageError = 'Failed to send audio: $error';
          isEvaluating = false;
        });
      }
    }
  }

  Future<void> _evaluateAnswer(String sentence) async {
    if (sentence.isEmpty || generatedQuestion == null || selectedTheme == null) {
      setState(() {
        languageError = 'Cannot evaluate empty answer';
        isEvaluating = false;
      });
      return;
    }

    try {
      final response = await http.post(
        Uri.parse('https://talktoai.in/speaking/evaluate_answer'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'sentence': sentence,
          'theme_name': selectedTheme,
          'user_id': staticUserId,
          'question': generatedQuestion!['question'],
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['error'] != null) {
          if (mounted) {
            setState(() {
              languageError = data['error'];
              isEvaluating = false;
            });
          }
        } else {
          if (mounted) {
            setState(() {
              evaluation = data;
              isEvaluating = false;
            });
            _fadeController.forward(from: 0.0);
            Future.delayed(const Duration(milliseconds: 200), () {
              if (mounted) {
                _scrollToSection(evaluationSectionKey);
              }
            });
          }
        }
      } else {
        if (mounted) {
          setState(() {
            languageError = 'Evaluation failed: ${response.statusCode}';
            isEvaluating = false;
          });
        }
      }
    } catch (error) {
      if (mounted) {
        setState(() {
          languageError = 'Error evaluating answer: $error';
          isEvaluating = false;
        });
      }
    }
  }

  String cleanFeedback(String feedback) {
    return feedback.replaceAll(RegExp(r'\*\*|###'), '').trim();
  }

  void _toggleSuggestedAnswer() {
    setState(() {
      showSuggestedAnswer = !showSuggestedAnswer;
    });
  }

  Widget _buildThemeCard(String theme, BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 16),
      child: GestureDetector(
        onTap: recording
            ? null
            : () {
                setState(() {
                  selectedTheme = theme;
                  generatedQuestion = null;
                  evaluation = null;
                  transcription = '';
                  languageError = '';
                  showSuggestedAnswer = false; // Reset on theme change
                });
              },
        child: Card(
          elevation: 8,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          clipBehavior: Clip.antiAlias,
          child: Stack(
            children: [
              Positioned.fill(
                child: Image.network(
                  themeImages[theme]!,
                  fit: BoxFit.cover,
                  loadingBuilder: (context, child, loadingProgress) {
                    if (loadingProgress == null) return child;
                    return Center(
                      child: CircularProgressIndicator(
                        value: loadingProgress.expectedTotalBytes != null
                            ? loadingProgress.cumulativeBytesLoaded /
                                loadingProgress.expectedTotalBytes!
                            : null,
                      ),
                    );
                  },
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: Colors.grey[200],
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.broken_image, size: 48),
                            const SizedBox(height: 16),
                            Text(
                              theme,
                              style: const TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.bottomCenter,
                      end: Alignment.topCenter,
                      colors: [
                        Colors.black.withOpacity(0.8),
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
              ),
              Positioned(
                bottom: 32,
                left: 24,
                right: 24,
                child: Text(
                  theme,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    shadows: [
                      Shadow(
                        blurRadius: 10,
                        color: Colors.black,
                        offset: Offset(2, 2),
                      )
                    ],
                  ),
                ),
              ),
              if (selectedTheme == theme)
                Positioned(
                  top: 16,
                  right: 16,
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.check_circle,
                      color: Colors.green,
                      size: 36,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final cardHeight = screenHeight * 0.5;

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('Speaking Playground'),
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: isBackButtonDisabled
              ? null
              : () {
                  setState(() {
                    isBackButtonDisabled = true;
                  });
                  Navigator.of(context).pop();
                },
        ),
      ),
      body: SingleChildScrollView(
        controller: _scrollController,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Select a Theme',
                    style: TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Swipe horizontally to browse topics',
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(
              height: cardHeight,
              child: PageView.builder(
                controller: _pageController,
                itemCount: themeImages.length,
                itemBuilder: (context, index) {
                  final theme = themeImages.keys.elementAt(index);
                  return _buildThemeCard(theme, context);
                },
                onPageChanged: (index) {
                  final theme = themeImages.keys.elementAt(index);
                  setState(() {
                    selectedTheme = theme;
                    generatedQuestion = null;
                    evaluation = null;
                    transcription = '';
                    languageError = '';
                    showSuggestedAnswer = false;
                  });
                },
              ),
            ),
            const SizedBox(height: 24),
            if (selectedTheme != null)
              Padding(
                padding: const EdgeInsets.all(16.0),
                key: questionSectionKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Practice Question',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _generateQuestion,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 16),
                      ),
                      child: const Text(
                        'Generate Question',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),
                    AnimatedSwitcher(
                      duration: const Duration(milliseconds: 300),
                      child: generatedQuestion != null
                          ? Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  key: const ValueKey('generatedQuestion'),
                                  width: double.infinity,
                                  padding: const EdgeInsets.all(24),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(16),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.grey.withOpacity(0.1),
                                        spreadRadius: 2,
                                        blurRadius: 8,
                                        offset: const Offset(0, 4),
                                      ),
                                    ],
                                    border: Border.all(
                                      color: Colors.grey[200]!,
                                    ),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Text(
                                        'Question',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 18,
                                          color: Colors.blue,
                                        ),
                                      ),
                                      const SizedBox(height: 16),
                                      Text(
                                        generatedQuestion!['question'] ?? '',
                                        style: const TextStyle(
                                          fontSize: 16,
                                          color: Colors.black87,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 16),
                                TextButton(
                                  onPressed: _toggleSuggestedAnswer,
                                  child: Text(
                                    showSuggestedAnswer
                                        ? 'Hide Suggested Answer'
                                        : 'Show Suggested Answer',
                                    style: const TextStyle(
                                      fontSize: 16,
                                      color: Colors.blue,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                                if (showSuggestedAnswer) ...[
                                  const SizedBox(height: 16),
                                  Container(
                                    width: double.infinity,
                                    padding: const EdgeInsets.all(24),
                                    decoration: BoxDecoration(
                                      color: Colors.grey[100],
                                      borderRadius: BorderRadius.circular(16),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.grey.withOpacity(0.1),
                                          spreadRadius: 2,
                                          blurRadius: 8,
                                          offset: const Offset(0, 4),
                                        ),
                                      ],
                                      border: Border.all(
                                        color: Colors.grey[200]!,
                                      ),
                                    ),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        const Text(
                                          'Suggested Answer',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 18,
                                            color: Colors.blue,
                                          ),
                                        ),
                                        const SizedBox(height: 16),
                                        Text(
                                          generatedQuestion!['suggestedAnswer'] ??
                                              '',
                                          style: const TextStyle(
                                            fontSize: 16,
                                            color: Colors.black87,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ],
                            )
                          : Container(
                              padding: const EdgeInsets.all(24),
                              decoration: BoxDecoration(
                                color: Colors.grey[50],
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: Colors.grey[200]!,
                                ),
                              ),
                              child: const Center(
                                child: Text(
                                  'Generate a question to get started',
                                  style: TextStyle(
                                    color: Colors.grey,
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                            ),
                    ),
                  ],
                ),
              ),
            if (generatedQuestion != null) ...[
              const SizedBox(height: 24),
              Padding(
                padding: const EdgeInsets.all(16.0),
                key: recordSectionKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Record Your Answer',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        ElevatedButton(
                          onPressed: recording ? null : _startRecording,
                          style: ElevatedButton.styleFrom(
                            backgroundColor:
                                recording ? Colors.red : Colors.blue,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 24, vertical: 16),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                recording ? Icons.mic : Icons.mic_none,
                                color: Colors.white,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                recording
                                    ? 'Recording ($_recordingDuration s)'
                                    : 'Start Recording',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (recording) ...[
                          const SizedBox(width: 16),
                          ElevatedButton(
                            onPressed: _stopRecording,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 24, vertical: 16),
                            ),
                            child: const Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(Icons.stop, color: Colors.white),
                                SizedBox(width: 8),
                                Text(
                                  'Stop',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                    const SizedBox(height: 24),
                    if (isEvaluating)
                      const Column(
                        children: [
                          CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                          ),
                          SizedBox(height: 16),
                          Text(
                            'Processing your answer...',
                            style: TextStyle(
                              color: Colors.grey,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    if (!recording &&
                        transcription.isEmpty &&
                        languageError.isEmpty &&
                        !isEvaluating)
                      const Center(
                        child: Text(
                          'Press the microphone button to record your answer',
                          style: TextStyle(
                            color: Colors.grey,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    if (transcription.isNotEmpty)
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.grey[50],
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Colors.grey[200]!,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Your Answer',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 18,
                                color: Colors.blue,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              transcription,
                              style: const TextStyle(
                                fontSize: 16,
                                color: Colors.black87,
                              ),
                            ),
                          ],
                        ),
                      ),
                    if (languageError.isNotEmpty)
                      Container(
                        width: double.infinity,
                        margin: const EdgeInsets.only(top: 16),
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.red[50],
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Colors.red[200]!,
                          ),
                        ),
                        child: Text(
                          languageError,
                          style: const TextStyle(
                            color: Colors.red,
                            fontSize: 16,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ],
            if (evaluation != null) ...[
              const SizedBox(height: 24),
              FadeTransition(
                opacity: _fadeAnimation,
                key: evaluationSectionKey,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Evaluation Results',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.green[50],
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Colors.green[200]!,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Feedback',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 18,
                                color: Colors.green,
                              ),
                            ),
                            const SizedBox(height: 8),
                            ...cleanFeedback(evaluation!['feedback'] as String)
                                .split('\n\n')
                                .map((item) => Padding(
                                      padding: const EdgeInsets.only(top: 8.0),
                                      child: Text(
                                        item,
                                        style: const TextStyle(
                                          fontSize: 16,
                                          color: Colors.black87,
                                        ),
                                      ),
                                    )),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                      if (evaluation!['suggestions'] != null)
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.blue[50],
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: Colors.blue[200]!,
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Suggestions for Improvement',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18,
                                  color: Colors.blue,
                                ),
                              ),
                              const SizedBox(height: 8),
                              ...(evaluation!['suggestions'] as List<dynamic>)
                                  .map<Widget>((suggestion) => Padding(
                                        padding:
                                            const EdgeInsets.only(top: 8.0),
                                        child: Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            const Icon(
                                              Icons.arrow_right,
                                              color: Colors.blue,
                                              size: 24,
                                            ),
                                            const SizedBox(width: 8),
                                            Expanded(
                                              child: Text(
                                                suggestion.toString(),
                                                style: const TextStyle(
                                                  fontSize: 16,
                                                  color: Colors.black87,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ))
                                  .toList(),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ],
            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }
}
