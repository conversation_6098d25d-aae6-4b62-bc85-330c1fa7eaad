import 'dart:math' as math;
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Screens
import 'package:e_sasthra/Modules/antonyms_screen.dart';
import 'package:e_sasthra/Modules/homophones_screen.dart';
import 'package:e_sasthra/Modules/idioms_screen.dart';
import 'package:e_sasthra/Modules/speaking_screen.dart';
import 'package:e_sasthra/Modules/synonyms_screen.dart';
import 'package:e_sasthra/Modules/tenses_screen.dart';
import 'package:e_sasthra/Modules/writing_screen.dart' as writing_module;
import 'package:e_sasthra/Screens/listening_screen.dart';
import 'package:e_sasthra/Screens/writing_assessment.dart';

// Color system (3-5 colors total)
// Primary: Blue (brand), Neutrals: white/black/gray, Accent: Amber
const _brandPrimary = Color(0xFF2563EB); // blue-600
const _neutralBgDark = Color(0xFF0C0C0C);
const _neutralBgLight = Color(0xFFF7F7F9);
const _neutralCardLight = Colors.white;
const _accentAmber = Color(0xFFF59E0B); // amber-500

class ExploreScreen extends StatelessWidget {
  const ExploreScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final activeButtonData = <Map<String, dynamic>>[
      {'name': 'writing', 'screen': const writing_module.WritingPlaygroundPage()},
      {'name': 'homophones', 'screen': const HomophonesScreen()},
      {'name': 'idioms', 'screen': const IdiomsPage()},
      {'name': 'speaking', 'screen': const SpeakingPlayground()},
      {'name': 'grammar', 'screen': const WritingAssessment()},
      {'name': 'Listening', 'screen': const ListeningScreen()},
    ];

    final comingSoonButtonData = <Map<String, dynamic>>[
      {'name': 'antonyms', 'screen': const AntonymsScreen()},
      {'name': 'synonyms', 'screen': const SynonymsScreen()},
      {'name': 'tenses', 'screen': const TensesScreen()},
    ];

    return Scaffold(
      body: Stack(
        children: [
          const Positioned.fill(child: _AnimatedGradientBackground()),
          // Soft vignette for depth
          Positioned.fill(
            child: IgnorePointer(
              child: DecoratedBox(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.black.withOpacity(0.15),
                      Colors.transparent,
                      Colors.black.withOpacity(0.15),
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                ),
              ),
            ),
          ),
          SafeArea(
            child: CustomScrollView(
              slivers: [
                SliverAppBar(
                  backgroundColor: Colors.transparent,
                  elevation: 0,
                  pinned: true,
                  expandedHeight: 160,
                  flexibleSpace: FlexibleSpaceBar(
                    centerTitle: false,
                    titlePadding: const EdgeInsetsDirectional.only(start: 16, bottom: 16),
                    title: const _GlowTitle(text: 'Explore More'),
                    background: const _HeaderWave(),
                  ),
                ),

                // Active grid
                SliverPadding(
                  padding: const EdgeInsets.fromLTRB(12, 8, 12, 0),
                  sliver: _buildFeatureGrid(
                    context,
                    activeButtonData,
                    startDelayMs: 60,
                    comingSoon: false,
                  ),
                ),

                // Coming soon label
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(16, 18, 16, 8),
                    child: Row(
                      children: [
                        const _SectionDot(),
                        const SizedBox(width: 8),
                        Text(
                          'Coming Soon',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.w700,
                                color: Colors.white,
                              ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Coming soon grid
                SliverPadding(
                  padding: const EdgeInsets.fromLTRB(12, 8, 12, 16),
                  sliver: _buildFeatureGrid(
                    context,
                    comingSoonButtonData,
                    startDelayMs: 40,
                    comingSoon: true,
                  ),
                ),

                const SliverToBoxAdapter(child: SizedBox(height: 24)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  SliverGrid _buildFeatureGrid(
    BuildContext context,
    List<Map<String, dynamic>> data, {
    required int startDelayMs,
    required bool comingSoon,
  }) {
    return SliverGrid(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        mainAxisSpacing: 12,
        crossAxisSpacing: 12,
        childAspectRatio: 0.98,
      ),
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          final item = data[index];
          return _FeatureCard(
            label: item['name'] as String,
            screen: item['screen'] as Widget,
            comingSoon: comingSoon,
            // staggered entrance
            entranceDelay: Duration(milliseconds: startDelayMs * index),
          );
        },
        childCount: data.length,
      ),
    );
  }
}

class _AnimatedGradientBackground extends StatefulWidget {
  const _AnimatedGradientBackground();

  @override
  State<_AnimatedGradientBackground> createState() => _AnimatedGradientBackgroundState();
}

class _AnimatedGradientBackgroundState extends State<_AnimatedGradientBackground>
    with SingleTickerProviderStateMixin {
  late final AnimationController _ctrl;

  @override
  void initState() {
    super.initState();
    _ctrl = AnimationController(vsync: this, duration: const Duration(seconds: 10))..repeat();
  }

  @override
  void dispose() {
    _ctrl.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return AnimatedBuilder(
      animation: _ctrl,
      builder: (context, _) {
        final t = _ctrl.value * 2 * math.pi;
        final begin = Alignment(math.cos(t) * 0.6, math.sin(t) * 0.6);
        final end = Alignment(-math.cos(t) * 0.6, -math.sin(t) * 0.6);

        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: begin,
              end: end,
              colors: isDark
                  ? [
                      _neutralBgDark,
                      const Color(0xFF0F172A), // slate-900
                      const Color(0xFF0B1220), // deep navy
                    ]
                  : [
                      _neutralBgLight,
                      const Color(0xFFEFF4FF),
                      const Color(0xFFEAF2FF),
                    ],
            ),
          ),
        );
      },
    );
  }
}

class _GlowTitle extends StatefulWidget {
  final String text;
  const _GlowTitle({required this.text});

  @override
  State<_GlowTitle> createState() => _GlowTitleState();
}

class _GlowTitleState extends State<_GlowTitle> with SingleTickerProviderStateMixin {
  late final AnimationController _ctrl;
  late final Animation<double> _glow;

  @override
  void initState() {
    super.initState();
    _ctrl = AnimationController(vsync: this, duration: const Duration(seconds: 2))..repeat(reverse: true);
    _glow = Tween<double>(begin: 6, end: 16).animate(CurvedAnimation(parent: _ctrl, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _ctrl.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _glow,
      builder: (context, _) {
        return Text(
          widget.text,
          overflow: TextOverflow.ellipsis,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w800,
                color: Colors.white,
                shadows: [
                  Shadow(color: _brandPrimary.withOpacity(0.8), blurRadius: _glow.value),
                ],
              ),
        );
      },
    );
  }
}

class _HeaderWave extends StatelessWidget {
  const _HeaderWave();

  @override
  Widget build(BuildContext context) {
    // Subtle animated gloss over the header area
    return Stack(
      fit: StackFit.expand,
      children: [
        Align(
          alignment: Alignment.bottomCenter,
          child: ClipPath(
            clipper: _WaveClipper(),
            child: Container(
              height: 140,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    _brandPrimary.withOpacity(0.25),
                    _brandPrimary.withOpacity(0.05),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class _WaveClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final p = Path();
    p.lineTo(0, size.height - 30);
    p.quadraticBezierTo(size.width * 0.25, size.height, size.width * 0.5, size.height - 30);
    p.quadraticBezierTo(size.width * 0.75, size.height - 60, size.width, size.height - 20);
    p.lineTo(size.width, 0);
    p.close();
    return p;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) => false;
}

class _SectionDot extends StatelessWidget {
  const _SectionDot();

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 8,
      height: 22,
      decoration: BoxDecoration(
        color: _accentAmber,
        borderRadius: BorderRadius.circular(6),
        boxShadow: [
          BoxShadow(color: _accentAmber.withOpacity(0.5), blurRadius: 8, spreadRadius: 1),
        ],
      ),
    );
  }
}

class _FeatureCard extends StatefulWidget {
  final String label;
  final Widget screen;
  final bool comingSoon;
  final Duration entranceDelay;

  const _FeatureCard({
    required this.label,
    required this.screen,
    required this.comingSoon,
    required this.entranceDelay,
  });

  @override
  State<_FeatureCard> createState() => _FeatureCardState();
}

class _FeatureCardState extends State<_FeatureCard> with TickerProviderStateMixin {
  bool _visible = false;
  bool _pressed = false;

  // Overlay animations for "Coming Soon"
  late final AnimationController _lockCtrl;
  late final Animation<double> _lockScale;

  // Shimmer stripe
  late final AnimationController _shimmerCtrl;

  @override
  void initState() {
    super.initState();

    // Staggered entrance
    Future.delayed(widget.entranceDelay, () {
      if (mounted) setState(() => _visible = true);
    });

    _lockCtrl = AnimationController(vsync: this, duration: const Duration(milliseconds: 1400))
      ..repeat(reverse: true);
    _lockScale = Tween<double>(begin: 1.0, end: 1.15).animate(CurvedAnimation(parent: _lockCtrl, curve: Curves.easeInOut));

    _shimmerCtrl = AnimationController(vsync: this, duration: const Duration(milliseconds: 1800))
      ..repeat();
  }

  @override
  void dispose() {
    _lockCtrl.dispose();
    _shimmerCtrl.dispose();
    super.dispose();
  }

  String _getAssetPath(String label) {
    switch (label.toLowerCase()) {
      case 'homophones':
        return 'assets/images/Homophones.png';
      default:
        return 'assets/images/${label}.png';
    }
  }

  void _handleTap(BuildContext context) {
    if (widget.comingSoon) return;
    HapticFeedback.selectionClick();
    Navigator.of(context).push(_fadeScaleRoute(widget.screen));
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return AnimatedOpacity(
      opacity: _visible ? 1 : 0,
      duration: const Duration(milliseconds: 450),
      curve: Curves.easeOut,
      child: AnimatedSlide(
        offset: _visible ? Offset.zero : const Offset(0, 0.08),
        duration: const Duration(milliseconds: 450),
        curve: Curves.easeOutCubic,
        child: GestureDetector(
          onTapDown: (_) => setState(() => _pressed = true),
          onTapCancel: () => setState(() => _pressed = false),
          onTapUp: (_) => setState(() => _pressed = false),
          onTap: () => _handleTap(context),
          child: TweenAnimationBuilder<double>(
            tween: Tween(end: _pressed ? 0.97 : 1.0),
            duration: const Duration(milliseconds: 120),
            builder: (context, scale, child) {
              return Transform.scale(
                scale: scale,
                child: child,
              );
            },
            child: ClipRRect(
              borderRadius: BorderRadius.circular(18),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
                child: Container(
                  decoration: BoxDecoration(
                    color: isDark
                        ? Colors.white.withOpacity(0.06)
                        : _neutralCardLight.withOpacity(0.82),
                    borderRadius: BorderRadius.circular(18),
                    border: Border.all(
                      color: isDark ? Colors.white.withOpacity(0.08) : Colors.black.withOpacity(0.05),
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(isDark ? 0.35 : 0.08),
                        blurRadius: 18,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  padding: const EdgeInsets.all(10),
                  child: Stack(
                    children: [
                      // Content
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Expanded(
                            child: Hero(
                              tag: 'feature-${widget.label.toLowerCase()}',
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(14),
                                child: Image.asset(
                                  _getAssetPath(widget.label),
                                  fit: BoxFit.contain,
                                  width: double.infinity,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            widget.label[0].toUpperCase() + widget.label.substring(1),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              fontWeight: FontWeight.w700,
                              fontSize: 16,
                              color: widget.comingSoon
                                  ? Colors.grey[500]
                                  : (isDark ? Colors.white : Colors.black87),
                            ),
                          ),
                        ],
                      ),

                      // Coming Soon overlay
                      if (widget.comingSoon) Positioned.fill(child: _ComingSoonOverlay(lockScale: _lockScale, shimmerCtrl: _shimmerCtrl)),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class _ComingSoonOverlay extends StatelessWidget {
  final Animation<double> lockScale;
  final AnimationController shimmerCtrl;

  const _ComingSoonOverlay({
    required this.lockScale,
    required this.shimmerCtrl,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: shimmerCtrl,
      builder: (context, _) {
        final dx = (shimmerCtrl.value * 2) - 1; // -1 -> 1
        return Container(
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.35),
            borderRadius: BorderRadius.circular(18),
          ),
          child: Stack(
            children: [
              // Shimmer stripe
              Transform.translate(
                offset: Offset(dx * 220, 0),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Container(
                    width: 60,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.white.withOpacity(0.05),
                          Colors.white.withOpacity(0.18),
                          Colors.white.withOpacity(0.05),
                        ],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      ),
                    ),
                  ),
                ),
              ),
              // Lock + Label
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ScaleTransition(
                      scale: lockScale,
                      child: Icon(
                        Icons.lock_outline_rounded,
                        size: 40,
                        color: Colors.white,
                        shadows: [
                          Shadow(color: _accentAmber.withOpacity(0.8), blurRadius: 18),
                        ],
                      ),
                    ),
                    const SizedBox(height: 10),
                    const Text(
                      'Coming Soon',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w800,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

// Custom page transition: fade + slight scale
PageRouteBuilder _fadeScaleRoute(Widget page) {
  return PageRouteBuilder(
    transitionDuration: const Duration(milliseconds: 380),
    reverseTransitionDuration: const Duration(milliseconds: 260),
    pageBuilder: (context, animation, secondaryAnimation) => page,
    transitionsBuilder: (context, animation, secondaryAnimation, child) {
      final curved = CurvedAnimation(parent: animation, curve: Curves.easeOutCubic);
      return FadeTransition(
        opacity: curved,
        child: ScaleTransition(
          scale: Tween<double>(begin: 0.98, end: 1.0).animate(curved),
          child: child,
        ),
      );
    },
  );
}