import 'dart:convert';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';
import '../user_provider.dart';

class AuthService {
  static const String _baseUrl = 'https://talktoai.in';
  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  
  // Test user configuration
  static const String TEST_PHONE_NUMBER = '+12025550123';
  static const Map<String, dynamic> _testUser = {
    'userId': 'test_user_123',
    'firstName': 'Test',
    'lastName': 'User',
    'email': '<EMAIL>',
    'nationality': 'US',
    'nativeLanguage': 'English',
    'levelOfEnglish': 'Advanced',
    'userName': 'testuser123',
    'age': 25,
    'profession': 'Developer',
    'schoolClass': null,
    'reasonForLearning': 'Professional Development',
    'accessToken': 'test_access_token_123',
    'refreshToken': 'test_refresh_token_123',
    'defaultOtp': '123456',
  };

  /// Check if user is authenticated and return authentication status
  static Future<AuthStatus> checkAuthStatus() async {
    try {
      final accessToken = await _storage.read(key: 'access_token');
      final refreshToken = await _storage.read(key: 'refresh_token');
      var phoneNumber = await _storage.read(key: 'phone_number');
      
      // Normalize phone number
      if (phoneNumber != null) {
        phoneNumber = phoneNumber.replaceAll(' ', '');
      }

      // If no tokens, user is not authenticated
      if (accessToken == null) {
        return AuthStatus.notAuthenticated;
      }

      // Handle test user
      if (phoneNumber == TEST_PHONE_NUMBER) {
        return AuthStatus.authenticatedTestUser;
      }

      // Validate access token with server
      final isValidToken = await _validateAccessToken(accessToken);
      if (isValidToken) {
        return AuthStatus.authenticated;
      }

      // Try to refresh token if access token is invalid
      if (refreshToken != null) {
        final newAccessToken = await _refreshAccessToken(refreshToken);
        if (newAccessToken != null) {
          await _storage.write(key: 'access_token', value: newAccessToken);
          return AuthStatus.authenticated;
        }
      }

      // If all fails, clear tokens and return not authenticated
      await clearAuthData();
      return AuthStatus.notAuthenticated;
    } catch (e) {
      print('Error checking auth status: $e');
      return AuthStatus.notAuthenticated;
    }
  }

  /// Validate access token with server
  static Future<bool> _validateAccessToken(String accessToken) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/user-profile'),
        headers: {'Authorization': 'Bearer $accessToken'},
      ).timeout(const Duration(seconds: 10));

      return response.statusCode == 200;
    } catch (e) {
      print('Error validating access token: $e');
      return false;
    }
  }

  /// Refresh access token using refresh token
  static Future<String?> _refreshAccessToken(String refreshToken) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/refresh-token'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'refresh_token': refreshToken}),
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['access_token'];
      }
      return null;
    } catch (e) {
      print('Error refreshing access token: $e');
      return null;
    }
  }

  /// Fetch user profile from server
  static Future<Map<String, dynamic>?> fetchUserProfile([String? accessToken]) async {
    try {
      accessToken ??= await _storage.read(key: 'access_token');
      if (accessToken == null) return null;

      final response = await http.get(
        Uri.parse('$_baseUrl/user-profile'),
        headers: {'Authorization': 'Bearer $accessToken'},
      ).timeout(const Duration(seconds: 15));

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else if (response.statusCode == 401) {
        // Try to refresh token and retry
        final refreshToken = await _storage.read(key: 'refresh_token');
        if (refreshToken != null) {
          final newAccessToken = await _refreshAccessToken(refreshToken);
          if (newAccessToken != null) {
            await _storage.write(key: 'access_token', value: newAccessToken);
            return await fetchUserProfile(newAccessToken);
          }
        }
      }
      return null;
    } catch (e) {
      print('Error fetching user profile: $e');
      return null;
    }
  }

  /// Load user data into UserProvider
  static Future<bool> loadUserData(BuildContext context) async {
    try {
      final phoneNumber = await _storage.read(key: 'phone_number');
      final userProvider = Provider.of<UserProvider>(context, listen: false);

      // Handle test user
      if (phoneNumber?.replaceAll(' ', '') == TEST_PHONE_NUMBER) {
        await userProvider.setUserDetails(
          userId: _testUser['userId'],
          phoneNumber: phoneNumber,
          firstName: _testUser['firstName'],
          lastName: _testUser['lastName'],
          email: _testUser['email'],
          nationality: _testUser['nationality'],
          nativeLanguage: _testUser['nativeLanguage'],
          levelOfEnglish: _testUser['levelOfEnglish'],
          userName: _testUser['userName'],
          age: _testUser['age'],
          profilePicture: null,
          profession: _testUser['profession'],
          schoolClass: _testUser['schoolClass'],
          reasonForLearning: _testUser['reasonForLearning'],
        );
        return true;
      }

      // Load regular user profile
      final profile = await fetchUserProfile();
      if (profile != null) {
        await userProvider.setUserDetails(
          userId: profile['user_id']?.toString(),
          phoneNumber: profile['phone_number'],
          firstName: profile['first_name'],
          lastName: profile['last_name'],
          email: profile['email'],
          nationality: profile['nationality'],
          nativeLanguage: profile['native_language'],
          levelOfEnglish: profile['level_of_english'],
          userName: profile['user_name'],
          age: profile['age'],
          profilePicture: profile['profile_picture'],
          profession: profile['profession'],
          schoolClass: profile['school_class'],
          reasonForLearning: profile['reason_for_learning'],
        );
        return true;
      }
      return false;
    } catch (e) {
      print('Error loading user data: $e');
      return false;
    }
  }

  /// Store authentication tokens and phone number
  static Future<void> storeAuthData({
    required String accessToken,
    required String refreshToken,
    required String phoneNumber,
    bool isTestUser = false,
  }) async {
    await _storage.write(key: 'access_token', value: accessToken);
    await _storage.write(key: 'refresh_token', value: refreshToken);
    await _storage.write(key: 'phone_number', value: phoneNumber.replaceAll(' ', ''));
    await _storage.write(key: 'is_test_user', value: isTestUser.toString());
  }

  /// Clear all authentication data
  static Future<void> clearAuthData() async {
    await _storage.delete(key: 'access_token');
    await _storage.delete(key: 'refresh_token');
    await _storage.delete(key: 'phone_number');
    await _storage.delete(key: 'is_test_user');
  }

  /// Check if current user is test user
  static Future<bool> isTestUser() async {
    final phoneNumber = await _storage.read(key: 'phone_number');
    return phoneNumber?.replaceAll(' ', '') == TEST_PHONE_NUMBER;
  }

  /// Get test user data
  static Map<String, dynamic> getTestUserData() => _testUser;
}

enum AuthStatus {
  authenticated,
  authenticatedTestUser,
  notAuthenticated,
}
