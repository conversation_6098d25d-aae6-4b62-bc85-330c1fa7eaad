import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:e_sasthra/constants/app_colors.dart';

class About2Page extends StatefulWidget {
  const About2Page({super.key});

  @override
  State<About2Page> createState() => _About2PageState();
}

class _About2PageState extends State<About2Page>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _floatAnimation;
  bool _showText = false;

  @override
  void initState() {
    super.initState();

    // Floating animation
    _controller =
        AnimationController(vsync: this, duration: const Duration(seconds: 3))
          ..repeat(reverse: true);

    _floatAnimation =
        Tween<double>(begin: 0, end: 20).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    // Delay text fade-in
    Future.delayed(const Duration(milliseconds: 800), () {
      setState(() {
        _showText = true;
      });
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Widget buildImage(String path, double size) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        image: DecorationImage(
          image: AssetImage(path),
          fit: BoxFit.cover,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color.fromARGB(255, 134, 217, 209), // Lighter teal
              Color.fromARGB(255, 24, 195, 178), // Medium teal
              Color.fromARGB(255, 3, 127, 106), // Darker teal
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Image stack (all centered, bigger sizes)
              SizedBox(
                height: 400,
                width: 400,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    // Background image 1
                    buildImage('assets/images/about_1.png', 220),
                    // Background image 2
                    buildImage('assets/about_3.png', 220),
                    // Floating Center Image
                    AnimatedBuilder(
                      animation: _floatAnimation,
                      builder: (context, child) {
                        return Transform.translate(
                          offset: Offset(0, -_floatAnimation.value),
                          child: buildImage('assets/about_2.png', 280),
                        );
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 30),

              // Fade-in text
              AnimatedOpacity(
                opacity: _showText ? 1 : 0,
                duration: const Duration(seconds: 2),
                child: Text(
                  'Your AI-Powered Path to\nEnglish Fluency!',
                  textAlign: TextAlign.center,
                  style: GoogleFonts.rubik(
                    fontSize: 28,
                    fontWeight: FontWeight.w800,
                    color: AppColors.white,
                    shadows: [
                      Shadow(
                        offset: const Offset(2, 2),
                        blurRadius: 6,
                        color: Colors.black.withOpacity(0.4),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
